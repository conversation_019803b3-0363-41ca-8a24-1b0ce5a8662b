# 流式XML解析功能实现说明

## 功能概述

实现了支持流式处理的XML消息解析功能，能够在PRD需求文档不完整时，实时解析和渲染包含 `<status>` 和 `<amis_json>` 标签的XML响应。

## 关键特性

### 1. 流式解析支持
- 支持逐步接收XML内容，实时更新显示
- 在 `<status>` 标签未完整时显示脉冲光标效果
- 检测到 `<amis_json>` 开始时立即显示加载状态

### 2. 渐进式用户体验
- **第一步**：显示状态信息（Alert组件）
- **第二步**：显示"正在接收表单数据..."或"正在准备表单..."
- **第三步**：渲染完整的AMIS表单

### 3. 智能状态检测
```typescript
// 流式解析状态
{
  status?: string;           // 状态文本内容
  statusComplete: boolean;   // status标签是否完整
  amisJson?: any;           // 解析后的AMIS配置
  amisJsonComplete: boolean; // amis_json是否解析完成
  amisJsonStarted: boolean;  // amis_json是否开始接收
}
```

## 技术实现

### 核心组件

1. **StreamingXMLMessageParser** - 流式XML解析器
2. **XMLMessageParser** - 静态XML解析器（向后兼容）
3. **TextMessageCard** - 集成XML解析的文本消息卡片

### 解析逻辑

```typescript
// 检测status标签
const statusMatch = xmlContent.match(/<status>([\s\S]*?)<\/status>/);
if (statusMatch) {
  // 完整的status标签
  result.status = statusMatch[1].trim();
  result.statusComplete = true;
} else {
  // 未闭合的status标签
  const statusStartMatch = xmlContent.match(/<status>([\s\S]*?)$/);
  if (statusStartMatch) {
    result.status = statusStartMatch[1].trim();
    result.statusComplete = false;
  }
}
```

### 视觉效果

1. **状态显示**：黄色Alert组件，带脉冲光标
2. **加载状态**：灰色背景卡片，旋转loading图标
3. **表单渲染**：完整的AMIS表单组件

## 使用场景

当PRD解析器检测到需求文档不完整时：

1. 后端返回XML格式响应：
```xml
<status>您提供的产品需求文档还需要完善，缺少以下重要信息：
- 缺少具体的页面布局设计或原型图
- 缺少用户交互流程说明

为了帮助您完善需求，我准备了一个表单来收集这些信息。</status>
<amis_json>
{
  "type": "form",
  "title": "完善产品需求文档",
  "body": [...]
}
</amis_json>
```

2. 前端流式处理：
   - 收到 `<status>` 时立即显示状态信息
   - 收到 `<amis_json>` 时显示加载状态
   - 收到 `</amis_json>` 时渲染表单

## 向后兼容

- 保持对原有AMIS表单配置的支持
- 支持InterruptCard中的XML解析
- 不影响普通Markdown消息的渲染 
