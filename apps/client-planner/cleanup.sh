#!/bin/bash

# 清理 client-planner 中未使用的代码
echo "开始清理 client-planner 中未使用的代码..."

# 创建备份目录
BACKUP_DIR="/Users/<USER>/Desktop/dev/airstar-ai/apps/client-planner/backup_$(date +%Y%m%d%H%M%S)"
mkdir -p "$BACKUP_DIR"
echo "创建备份目录: $BACKUP_DIR"

# 删除未使用的依赖
echo "正在从 package.json 中移除未使用的依赖..."

# 备份 package.json
cp /Users/<USER>/Desktop/dev/airstar-ai/apps/client-planner/package.json "$BACKUP_DIR/package.json"

# 未使用的依赖列表
UNUSED_DEPS=(
  "@ai-sdk/react"
  "@copilotkit/react-core"
  "@copilotkit/react-textarea"
  "@copilotkit/react-ui"
  "@copilotkit/shared"
  "@langchain/core"
  "@langchain/openai"
  "@radix-ui/react-accordion"
  "@radix-ui/react-alert-dialog"
  "@radix-ui/react-aspect-ratio"
  "@radix-ui/react-avatar"
  "@radix-ui/react-checkbox"
  "@radix-ui/react-collapsible"
  "@radix-ui/react-context-menu"
  "@radix-ui/react-dialog"
  "@radix-ui/react-dropdown-menu"
  "@radix-ui/react-hover-card"
  "@radix-ui/react-icons"
  "@radix-ui/react-label"
  "@radix-ui/react-menubar"
  "@radix-ui/react-navigation-menu"
  "@radix-ui/react-popover"
  "@radix-ui/react-progress"
  "@radix-ui/react-radio-group"
  "@radix-ui/react-select"
  "@radix-ui/react-separator"
  "@radix-ui/react-slider"
  "@radix-ui/react-slot"
  "@radix-ui/react-switch"
  "@radix-ui/react-tabs"
  "@radix-ui/react-toast"
  "@radix-ui/react-toggle"
  "@radix-ui/react-toggle-group"
  "@radix-ui/react-tooltip"
  "ai"
  "class-variance-authority"
  "clsx"
  "cmdk"
  "date-fns"
  "embla-carousel-react"
  "framer-motion"
  "langchain"
  "lucide-react"
  "markdown-it"
  "next-themes"
  "openai"
  "react-day-picker"
  "react-hook-form"
  "react-markdown"
  "react-resizable-panels"
  "react-syntax-highlighter"
  "rehype-raw"
  "remark-gfm"
  "sonner"
  "tailwind-merge"
  "tailwindcss-animate"
  "tw-animate-css"
  "unist-util-visit"
  "use-debounce"
  "use-stick-to-bottom"
)

# 未使用的开发依赖列表
UNUSED_DEV_DEPS=(
  "@arco-plugins/vite-react"
  "@ks/weblogger"
  "@tailwindcss/line-clamp"
  "@tailwindcss/postcss"
  "eslint-config-prettier"
  "eslint-plugin-prettier"
  "eslint-plugin-unused-imports"
  "hast"
  "prettier-plugin-tailwindcss"
  "react-error-boundary"
)

# 使用 npm 移除未使用的依赖
echo "移除未使用的依赖..."
npm uninstall ${UNUSED_DEPS[@]} --prefix /Users/<USER>/Desktop/dev/airstar-ai/apps/client-planner

echo "移除未使用的开发依赖..."
npm uninstall ${UNUSED_DEV_DEPS[@]} --dev --prefix /Users/<USER>/Desktop/dev/airstar-ai/apps/client-planner

# 备份并删除未使用的导出
echo "备份并删除未使用的导出..."

# 未使用的导出文件列表
declare -A UNUSED_EXPORTS
UNUSED_EXPORTS=(
  ["apps/client-planner/src/components/SessionManager/CreateSessionButton.tsx"]="CreateSessionButton"
  ["apps/client-planner/src/components/ui/badge.tsx"]="badgeVariants"
  ["apps/client-planner/src/components/ui/button.tsx"]="buttonVariants"
  ["apps/client-planner/src/components/ui/card.tsx"]="CardFooter CardDescription"
  ["apps/client-planner/src/components/ui/dialog.tsx"]="DialogPortal DialogOverlay DialogClose DialogTrigger"
  ["apps/client-planner/src/components/ui/scroll-area.tsx"]="ScrollBar"
  ["apps/client-planner/src/components/ui/toast.tsx"]="ToastProvider ToastViewport Toast ToastTitle ToastDescription ToastClose ToastAction"
  ["apps/client-planner/src/constants/index.ts"]="DEFAULT_LOGO ROUTE_MAP REVERSE_ROUTE_MAP ROUTE_PAGE_NAME"
  ["apps/client-planner/src/constants/status.ts"]="STATUS_COLORS STATUS_LABELS TASK_STATUS_COLORS TASK_STATUS_LABELS"
  ["apps/client-planner/src/constants/ui.ts"]="SCROLL_HEIGHTS"
  ["apps/client-planner/src/context/Model/index.tsx"]="ModelContext Provider"
  ["apps/client-planner/src/hooks/use-toast.ts"]="reducer toast"
  ["apps/client-planner/src/service/api/chat.ts"]="fetchReplay fetchReplayTitle sleepInReplay fastForwardReplay"
  ["apps/client-planner/src/service/api/mcp.ts"]="queryMCPServerMetadata"
  ["apps/client-planner/src/service/api/session.ts"]="getSessionById getSessionByThreadId updateSession"
  ["apps/client-planner/src/service/mcp/schema.ts"]="MCPConfigSchema"
  ["apps/client-planner/src/service/mcp/utils.ts"]="findMCPTool"
  ["apps/client-planner/src/utils/api-error-handler.ts"]="createApiError retryApiCall"
  ["apps/client-planner/src/utils/json.ts"]="parseJSON"
)

# 备份并处理每个文件
for file in "${!UNUSED_EXPORTS[@]}"; do
  if [ -f "/Users/<USER>/Desktop/dev/airstar-ai/$file" ]; then
    # 创建备份目录结构
    mkdir -p "$BACKUP_DIR/$(dirname "$file")"
    
    # 备份文件
    cp "/Users/<USER>/Desktop/dev/airstar-ai/$file" "$BACKUP_DIR/$file"
    
    echo "已备份: $file"
    
    # 这里我们不直接删除文件，而是标记未使用的导出，以便后续手动处理
    echo "需要检查的未使用导出 ($file): ${UNUSED_EXPORTS[$file]}"
  fi
done

echo "清理完成！"
echo "备份已保存到: $BACKUP_DIR"
echo "请注意：未使用的导出已被标记，但未自动删除。请手动检查这些文件并删除未使用的导出。"
