{"private": true, "name": "client-planner", "author": "lijie14 <<EMAIL>>", "version": "0.0.1", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@copilotkit/react-core": "^1.8.13", "@copilotkit/react-ui": "^1.8.13", "@copilotkit/runtime": "^1.8.13", "@hookform/resolvers": "^5.0.1", "@monaco-editor/react": "^4.5.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/react": "^2.11.7", "@xyflow/react": "^12.6.0", "ahooks": "^3.3.0", "amis": "^6.12.0", "axios": "^1.3.2", "b-tween": "^0.3.3", "best-effort-json-parser": "^1.1.3", "class-variance-authority": "^0.7.1", "classnames": "^2.3.2", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compute-scroll-into-view": "^3.0.0", "copy-to-clipboard": "3.3.1", "dayjs": "^1.11.11", "framer-motion": "^12.6.5", "highlight.js": "^11.11.1", "html-to-image": "^1.11.11", "immer": "^10.1.1", "is-valid-domain": "^0.1.6", "json-ast-comments": "1.1.1", "katex": "^0.16.21", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lru-cache": "^11.1.0", "lucide-react": "^0.487.0", "monaco-editor": "^0.37.1", "motion": "^12.7.4", "nanoid": "^5.1.5", "novel": "^1.0.2", "nprogress": "^0.2.0", "optics-ts": "^2.4.0", "pubsub-js": "^1.9.4", "qs": "^6.11.0", "query-string": "^8.1.0", "react": "^18.3.1", "react-cropper": "^2.3.3", "react-dom": "^18.3.1", "react-hook-form": "^7.56.1", "react-joyride": "^2.5.5", "react-json-view": "^1.21.3", "react-markdown": "^10.1.0", "react-router-dom": "^6.8.0", "react-syntax-highlighter": "^15.6.1", "react-transition-group": "4.4.2", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "scroll-into-view-if-needed": "^3.0.6", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tiptap-markdown": "^0.8.10", "tw-animate-css": "^1.2.5", "unist-util-visit": "^5.0.0", "use-debounce": "^10.0.4", "use-stick-to-bottom": "^1.1.0", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@arco-plugins/vite-react": "^1.3.3", "@ks/weblogger": "3.10.33", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4.1.7", "@trivago/prettier-plugin-sort-imports": "^4.0.0", "@types/lodash": "^4.14.191", "@types/pubsub-js": "^1.8.3", "@types/qs": "^6.5.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-transition-group": "^4.4.5", "@typescript-eslint/eslint-plugin": "^5.52.0", "@vitejs/plugin-react": "^1.3.0", "autoprefixer": "^10.4.13", "eslint": "^8.33.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.21.5", "eslint-plugin-unused-imports": "^2.0.0", "hast": "^1.0.0", "less": "^4.1.3", "postcss": "^8.4.21", "prettier": "^2.8.4", "prettier-plugin-tailwindcss": "^0.6.11", "react-error-boundary": "^4.0.4", "tailwindcss": "^3.1.6", "tailwindcss-animate": "^1.0.7", "typescript": "^4.6.3", "vite": "^2.9.7", "vite-plugin-svgr": "^2.1.0", "vite-tsconfig-paths": "^4.0.5"}}