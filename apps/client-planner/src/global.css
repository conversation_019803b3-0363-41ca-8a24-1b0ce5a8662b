@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 83% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
}

@layer components {
  .linear-card {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .linear-button {
    background: linear-gradient(to right, #8b5cf6, #6366f1);
    @apply text-white font-medium rounded-xl px-6 py-3 hover:shadow-lg transition-all duration-300 transform hover:scale-105;
  }

  .linear-input {
    @apply bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl px-4 py-3 focus:ring-2 focus:ring-purple-500/50 focus:border-transparent transition-all duration-300;
  }

  .status-badge {
    @apply inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium;
  }

  .priority-high {
    @apply bg-red-100 text-red-700 border border-red-200;
  }

  .priority-medium {
    @apply bg-yellow-100 text-yellow-700 border border-yellow-200;
  }

  .priority-low {
    @apply bg-green-100 text-green-700 border border-green-200;
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
}

.scroll-area-container > div {
  display: block !important;
}
