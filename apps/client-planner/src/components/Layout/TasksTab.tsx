import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Thread } from '@/service/api/plan-types'
import { usePlanStore } from '@/store/planStore'
import { AlertCircle, CheckCircle, Circle, Clock, Copy, Plus, Target } from 'lucide-react'
import { useEffect } from 'react'

interface TasksTabProps {
  activeSession: Thread
}

export function TasksTab({ activeSession }: TasksTabProps) {
  const { tasks, tasksLoading, fetchTasks, updateTaskStatus, completeTask } = usePlanStore()

  // 当活跃线程改变时，获取任务列表
  useEffect(() => {
    if (activeSession?.id) {
      fetchTasks(activeSession.id)
    }
  }, [activeSession?.id, fetchTasks])

  // 复制到剪贴板
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      console.log(`${label} 已复制到剪贴板`)
    })
  }

  // 获取任务状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className='h-4 w-4 text-green-500' />
      case 'in_progress':
        return <AlertCircle className='h-4 w-4 text-yellow-500' />
      case 'pending':
        return <Circle className='h-4 w-4 text-gray-400' />
      default:
        return <Circle className='h-4 w-4 text-gray-400' />
    }
  }

  // 获取任务状态样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700 border-green-200'
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'pending':
        return 'bg-gray-100 text-gray-700 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  // 获取优先级样式
  const getPriorityBadge = (priority: number) => {
    switch (priority) {
      case 3:
        return 'bg-red-100 text-red-700 border-red-200'
      case 2:
        return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 1:
        return 'bg-green-100 text-green-700 border-green-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getPriorityLabel = (priority: number) => {
    switch (priority) {
      case 3:
        return '高优先级'
      case 2:
        return '中优先级'
      case 1:
        return '低优先级'
      default:
        return '普通'
    }
  }

  // 处理任务状态更新
  const handleStatusUpdate = async (taskId: string, newStatus: 'pending' | 'in_progress' | 'completed' | 'deleted') => {
    try {
      await updateTaskStatus(taskId, { status: newStatus })
      // 重新获取任务列表
      if (activeSession?.id) {
        fetchTasks(activeSession.id)
      }
    } catch (error) {
      console.error('更新任务状态失败:', error)
    }
  }

  // 处理任务完成
  const handleCompleteTask = async (taskId: string) => {
    try {
      await completeTask(taskId, {
        completion_notes: '任务已完成',
      })
      // 重新获取任务列表
      if (activeSession?.id) {
        fetchTasks(activeSession.id)
      }
    } catch (error) {
      console.error('完成任务失败:', error)
    }
  }

  if (tasksLoading) {
    return (
      <div className='text-center py-16'>
        <div className='animate-spin h-8 w-8 border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-3'></div>
        <p className='text-sm text-gray-500'>加载任务中...</p>
      </div>
    )
  }

  if (!tasks || tasks.length === 0) {
    return (
      <div className='text-center py-16'>
        <div className='w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4'>
          <Target className='h-8 w-8 text-gray-400' />
        </div>
        <h3 className='text-lg font-medium text-gray-900 mb-2'>还没有生成任务列表</h3>
        <p className='text-gray-600 text-sm'>请先在 AI 对话中完善 PRD 信息</p>
      </div>
    )
  }

  return (
    <div className='h-full flex flex-col p-6'>
      <div className='flex items-center justify-between mb-6 flex-shrink-0'>
        <div>
          <h3 className='text-lg font-semibold text-gray-900'>任务列表</h3>
          <p className='text-sm text-gray-600'>共 {tasks.length} 个任务</p>
        </div>
        <Button
          onClick={() => copyToClipboard(JSON.stringify(tasks, null, 2), '任务列表')}
          variant='outline'
          size='sm'
          className='rounded-xl'
        >
          <Copy className='h-4 w-4 mr-2' />
          复制
        </Button>
      </div>

      <ScrollArea className='flex-1'>
        <div className='grid gap-4 pr-4'>
          {tasks.map((task, index) => (
            <div
              key={task.id}
              className='bg-white/60 border border-gray-200 rounded-xl p-5 hover:shadow-md transition-all duration-300 animate-fade-in'
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className='flex items-start justify-between mb-3'>
                <div className='flex items-start gap-3 flex-1'>
                  <button
                    onClick={() => {
                      if (task.status === 'completed') {
                        handleStatusUpdate(task.id, 'in_progress')
                      } else {
                        handleCompleteTask(task.id)
                      }
                    }}
                    className='mt-1'
                  >
                    {getStatusIcon(task.status)}
                  </button>
                  <div className='flex-1'>
                    <h4 className='font-medium text-gray-900 text-lg mb-1'>{task.title}</h4>
                    {task.description && <p className='text-gray-600 mb-3 leading-relaxed'>{task.description}</p>}
                  </div>
                </div>
                <div className='flex items-center gap-2 ml-4'>
                  <div className={`status-badge ${getStatusBadge(task.status)}`}>
                    {task.status === 'completed' ? '已完成' : task.status === 'in_progress' ? '进行中' : '待开始'}
                  </div>
                  {task.priority && (
                    <div className={`status-badge ${getPriorityBadge(task.priority)}`}>
                      {getPriorityLabel(task.priority)}
                    </div>
                  )}
                </div>
              </div>

              <div className='flex items-center gap-6 text-sm text-gray-500'>
                <div className='flex items-center gap-1'>
                  <Clock className='h-4 w-4' />
                  <span>类型: {task.type}</span>
                </div>
                {task.progress !== undefined && (
                  <div className='flex items-center gap-1'>
                    <span>进度: {task.progress}%</span>
                  </div>
                )}
                <div className='flex items-center gap-1'>
                  <span>创建于 {new Date(task.created_at).toLocaleDateString()}</span>
                </div>
              </div>

              {task.dependencies && task.dependencies.length > 0 && (
                <div className='mt-3 pt-3 border-t border-gray-100'>
                  <p className='text-sm text-gray-500'>依赖任务: {task.dependencies.join(', ')}</p>
                </div>
              )}

              {task.deployment_url && (
                <div className='mt-3 pt-3 border-t border-gray-100'>
                  <a
                    href={task.deployment_url}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='text-sm text-blue-600 hover:text-blue-800'
                  >
                    查看部署 →
                  </a>
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}
