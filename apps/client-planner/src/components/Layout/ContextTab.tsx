import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Thread } from '@/service/api/plan-types'
import { usePlanStore } from '@/store/planStore'
import { Copy, FileText, Image, List, Code, MessageSquare } from 'lucide-react'
import { useState } from 'react'

interface ContextTabProps {
  activeSession: Thread
}

export function ContextTab({ activeSession }: ContextTabProps) {
  const { messages } = usePlanStore()
  const [copiedField, setCopiedField] = useState<string | null>(null)

  // 从消息中提取上下文信息
  const extractContextFromMessages = () => {
    const context = {
      originalPRD: '',
      structuredPRD: '',
      images: [] as string[],
      prdAnalysis: '',
      processingStatus: '',
    }

    // 查找包含PRD信息的消息
    messages.forEach((message) => {
      if (message.role === 'user' && message.content) {
        // 用户输入的PRD
        if (!context.originalPRD) {
          context.originalPRD = message.content
        }
      }

      if (message.role === 'assistant' && message.content) {
        // 查找结构化PRD
        const fileMatch = message.content.match(/<file[^>]*>([\s\S]*?)<\/file>/)
        if (fileMatch) {
          context.structuredPRD = fileMatch[1].trim()
        }

        // 查找PRD分析结果
        const statusMatch = message.content.match(/<status>([\s\S]*?)<\/status>/)
        if (statusMatch) {
          context.prdAnalysis = statusMatch[1].trim()
        }
      }
    })

    return context
  }

  const context = extractContextFromMessages()

  // 复制到剪贴板
  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(fieldName)
      setTimeout(() => setCopiedField(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  // 格式化JSON显示
  const formatJSON = (obj: any) => {
    try {
      return JSON.stringify(obj, null, 2)
    } catch {
      return String(obj)
    }
  }

  return (
    <div className='h-full flex flex-col p-6'>
      <div className='flex items-center justify-between mb-6 flex-shrink-0'>
        <div>
          <h3 className='text-lg font-semibold text-gray-900'>会话上下文</h3>
          <p className='text-sm text-gray-600'>查看当前会话的中间处理状态和数据</p>
        </div>
      </div>

      <ScrollArea className='flex-1'>
        <Tabs defaultValue='prd' className='w-full'>
          <TabsList className='grid grid-cols-4 bg-gray-100/50 p-1 rounded-xl mb-6'>
            <TabsTrigger value='prd' className='rounded-lg'>
              <FileText className='h-4 w-4 mr-2' />
              PRD信息
            </TabsTrigger>
            <TabsTrigger value='structured' className='rounded-lg'>
              <Code className='h-4 w-4 mr-2' />
              结构化文档
            </TabsTrigger>
            <TabsTrigger value='analysis' className='rounded-lg'>
              <MessageSquare className='h-4 w-4 mr-2' />
              分析结果
            </TabsTrigger>
            <TabsTrigger value='thread' className='rounded-lg'>
              <List className='h-4 w-4 mr-2' />
              会话信息
            </TabsTrigger>
          </TabsList>

          <TabsContent value='prd' className='space-y-4'>
            <Card>
              <CardHeader className='pb-3'>
                <div className='flex items-center justify-between'>
                  <CardTitle className='text-base font-medium'>原始PRD</CardTitle>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => copyToClipboard(context.originalPRD, 'originalPRD')}
                    className='h-8'
                  >
                    <Copy className='h-3 w-3 mr-1' />
                    {copiedField === 'originalPRD' ? '已复制' : '复制'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {context.originalPRD ? (
                  <div className='bg-gray-50 rounded-lg p-4 text-sm font-mono whitespace-pre-wrap max-h-60 overflow-y-auto'>
                    {context.originalPRD}
                  </div>
                ) : (
                  <div className='text-gray-500 text-sm italic'>暂无原始PRD信息</div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='structured' className='space-y-4'>
            <Card>
              <CardHeader className='pb-3'>
                <div className='flex items-center justify-between'>
                  <CardTitle className='text-base font-medium'>结构化PRD文档</CardTitle>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => copyToClipboard(context.structuredPRD, 'structuredPRD')}
                    className='h-8'
                  >
                    <Copy className='h-3 w-3 mr-1' />
                    {copiedField === 'structuredPRD' ? '已复制' : '复制'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {context.structuredPRD ? (
                  <div className='bg-gray-50 rounded-lg p-4 text-sm font-mono whitespace-pre-wrap max-h-96 overflow-y-auto'>
                    {context.structuredPRD}
                  </div>
                ) : (
                  <div className='text-gray-500 text-sm italic'>暂无结构化PRD文档</div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='analysis' className='space-y-4'>
            <Card>
              <CardHeader className='pb-3'>
                <div className='flex items-center justify-between'>
                  <CardTitle className='text-base font-medium'>PRD分析结果</CardTitle>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => copyToClipboard(context.prdAnalysis, 'prdAnalysis')}
                    className='h-8'
                  >
                    <Copy className='h-3 w-3 mr-1' />
                    {copiedField === 'prdAnalysis' ? '已复制' : '复制'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {context.prdAnalysis ? (
                  <div className='bg-gray-50 rounded-lg p-4 text-sm whitespace-pre-wrap max-h-60 overflow-y-auto'>
                    {context.prdAnalysis}
                  </div>
                ) : (
                  <div className='text-gray-500 text-sm italic'>暂无分析结果</div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='thread' className='space-y-4'>
            <Card>
              <CardHeader className='pb-3'>
                <div className='flex items-center justify-between'>
                  <CardTitle className='text-base font-medium'>会话详细信息</CardTitle>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => copyToClipboard(formatJSON(activeSession), 'threadInfo')}
                    className='h-8'
                  >
                    <Copy className='h-3 w-3 mr-1' />
                    {copiedField === 'threadInfo' ? '已复制' : '复制'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <label className='font-medium text-gray-700'>会话ID：</label>
                    <span className='text-gray-600'>{activeSession.id}</span>
                  </div>
                  <div>
                    <label className='font-medium text-gray-700'>标题：</label>
                    <span className='text-gray-600'>{activeSession.title}</span>
                  </div>
                  <div>
                    <label className='font-medium text-gray-700'>状态：</label>
                    <span className='text-gray-600'>{activeSession.status}</span>
                  </div>
                  <div>
                    <label className='font-medium text-gray-700'>创建时间：</label>
                    <span className='text-gray-600'>{new Date(activeSession.created_at).toLocaleString()}</span>
                  </div>
                  <div>
                    <label className='font-medium text-gray-700'>更新时间：</label>
                    <span className='text-gray-600'>{new Date(activeSession.updated_at).toLocaleString()}</span>
                  </div>
                  <div>
                    <label className='font-medium text-gray-700'>消息数量：</label>
                    <span className='text-gray-600'>{messages.length}</span>
                  </div>
                </div>

                <div className='bg-gray-50 rounded-lg p-4'>
                  <h4 className='font-medium text-gray-700 mb-2'>完整会话数据：</h4>
                  <div className='bg-white rounded border p-3 text-xs font-mono max-h-60 overflow-y-auto'>
                    {formatJSON(activeSession)}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </ScrollArea>
    </div>
  )
}
