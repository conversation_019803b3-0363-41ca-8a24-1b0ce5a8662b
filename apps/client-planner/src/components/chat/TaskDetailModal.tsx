import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { CheckCircle, Clock, Info, Pause, XCircle } from 'lucide-react'
import React from 'react'

// 从 types.ts 导入 TaskStatus 类型 (假设路径，如果不同需调整)
// 我们直接使用从 MessageMetadata 推断出的类型结构
interface TaskStatusInfo {
  status: 'started' | 'in_progress' | 'completed' | 'failed' | 'paused'
  taskId?: string
  taskName?: string
  progress?: number
  message?: string
}

interface TaskDetailModalProps {
  isOpen: boolean
  onClose: () => void
  task: TaskStatusInfo | null
}

const getStatusTextAndIcon = (status: string | undefined) => {
  switch (status) {
    case 'started':
      return { text: '已开始', icon: <Clock className='h-5 w-5 text-blue-500' />, color: 'text-blue-500' }
    case 'in_progress':
      return {
        text: '进行中',
        icon: <Clock className='h-5 w-5 text-yellow-500 animate-spin' />,
        color: 'text-yellow-500',
      }
    case 'completed':
      return { text: '已完成', icon: <CheckCircle className='h-5 w-5 text-green-500' />, color: 'text-green-500' }
    case 'failed':
      return { text: '失败', icon: <XCircle className='h-5 w-5 text-red-500' />, color: 'text-red-500' }
    case 'paused':
      return { text: '已暂停', icon: <Pause className='h-5 w-5 text-gray-500' />, color: 'text-gray-500' }
    default:
      return { text: '未知状态', icon: <Info className='h-5 w-5 text-gray-400' />, color: 'text-gray-400' }
  }
}

export const TaskDetailModal: React.FC<TaskDetailModalProps> = ({ isOpen, onClose, task }) => {
  if (!task) return null

  const { text: statusText, icon: statusIcon, color: statusColor } = getStatusTextAndIcon(task.status)

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className='sm:max-w-[525px]'>
        <DialogHeader>
          <DialogTitle className='text-xl font-semibold'>{task.taskName || '任务详情'}</DialogTitle>
          {task.taskId && (
            <DialogDescription className='text-xs text-gray-500'>任务 ID: {task.taskId}</DialogDescription>
          )}
        </DialogHeader>
        <div className='grid gap-4 py-4'>
          <div className='flex items-center space-x-2'>
            <span className={`font-medium ${statusColor}`}>{statusIcon}</span>
            <Badge
              variant={task.status === 'completed' ? 'default' : 'outline'}
              className={
                task.status === 'completed'
                  ? 'bg-green-100 text-green-700 border-green-200'
                  : task.status === 'failed'
                  ? 'bg-red-100 text-red-700 border-red-200'
                  : task.status === 'in_progress'
                  ? 'bg-yellow-100 text-yellow-700 border-yellow-200'
                  : 'border-gray-300'
              }
            >
              {statusText}
            </Badge>
          </div>

          {task.progress !== undefined && (
            <div>
              <div className='mb-1 flex justify-between items-center'>
                <span className='text-sm font-medium text-gray-700'>进度</span>
                <span className='text-sm font-semibold text-gray-900'>{task.progress}%</span>
              </div>
              <Progress value={task.progress} className='h-2' />
            </div>
          )}

          {task.message && (
            <div>
              <h4 className='text-sm font-medium text-gray-700 mb-1'>相关信息:</h4>
              <p className='text-sm text-gray-600 bg-gray-50 p-3 rounded-md whitespace-pre-wrap'>{task.message}</p>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button onClick={onClose} variant='outline'>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
