import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import type { Message } from '@/service/messages/types'
import {
  AlertCircle,
  AlertTriangle,
  ArrowRight,
  CheckCircle,
  ClipboardList,
  Edit3,
  Save,
  X,
  XCircle,
} from 'lucide-react'
import { useState } from 'react'

import { AmisFormRenderer } from './amis-form-renderer'
import { XMLMessageParser, isXMLContent } from './xml-message-parser'

// 错误卡片
export function ErrorCard({ message }: { message: Message }) {
  const error = message.metadata?.error

  if (!error) return null

  return (
    <Alert className='border-red-200 bg-red-50'>
      <AlertCircle className='h-4 w-4 text-red-500' />
      <AlertTitle className='text-red-800'>发生错误</AlertTitle>
      <AlertDescription className='text-red-700'>
        <p className='mb-2'>{error.error}</p>
        {error.code && <p className='text-xs text-red-600'>错误代码: {error.code}</p>}
        {error.recoverable && <p className='text-xs text-red-600 mt-2'>这是一个可恢复的错误，您可以重试操作。</p>}
      </AlertDescription>
    </Alert>
  )
}

// 中断/反馈卡片
export function InterruptCard({ message, onFeedback }: { message: Message; onFeedback?: (feedback: string) => void }) {
  if (!message.options || message.options.length === 0) return null

  // 检查是否是 planner feedback 类型
  if (message.metadata?.feedbackType === 'planner') {
    return <PlannerFeedbackCard message={message} onFeedback={onFeedback} />
  }

  // 处理表单提交
  const handleFormSubmit = (formData: any) => {
    // 将表单数据转换为字符串格式发送给后端
    const feedbackData = JSON.stringify(formData)
    onFeedback?.(feedbackData)
  }

  // 检查消息内容是否为XML格式
  const isXML = isXMLContent(message.content)

  // 检查是否有AMIS表单配置（非XML格式）
  const hasAmisForm =
    !isXML &&
    (message.metadata?.amisForm ||
      message.metadata?.formConfig ||
      (message.metadata &&
        typeof message.metadata === 'object' &&
        'type' in message.metadata &&
        message.metadata.type === 'form'))

  return (
    <Card className='w-full bg-yellow-50 border-yellow-200'>
      <CardHeader className='pb-3'>
        <CardTitle className='flex items-center gap-2 text-sm'>
          <AlertTriangle className='h-4 w-4 text-yellow-600' />
          需要您的反馈
        </CardTitle>
      </CardHeader>
      <CardContent className='pt-0'>
        {/* 如果是XML格式，使用XML解析器 */}
        {isXML ? (
          <XMLMessageParser xmlContent={message.content} onFormSubmit={handleFormSubmit} />
        ) : (
          <>
            <p className='text-sm text-gray-700 mb-4'>{message.content}</p>

            {/* 如果有AMIS表单配置，渲染表单 */}
            {hasAmisForm && (
              <div className='mb-4'>
                <AmisFormRenderer
                  formConfig={message.metadata?.amisForm || message.metadata?.formConfig || message.metadata}
                  onSubmit={handleFormSubmit}
                />
              </div>
            )}
          </>
        )}

        {/* 选项按钮 */}
        {!isXML && (
          <div className='flex flex-wrap gap-2'>
            {message.options.map((option, index) => (
              <Button
                key={index}
                variant='outline'
                size='sm'
                onClick={() => onFeedback?.(option.value)}
                className='text-sm'
              >
                {option.text}
              </Button>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 规划器反馈卡片
export function PlannerFeedbackCard({
  message,
  onFeedback,
}: {
  message: Message
  onFeedback?: (feedback: string) => void
}) {
  const [isModifying, setIsModifying] = useState(false)
  const [newTaskList, setNewTaskList] = useState('')

  const taskBreakdown = message.metadata?.taskBreakdown
  const summary = message.metadata?.summary
  const taskJSON = message.metadata?.taskJSON

  // 处理批准继续
  const handleApprove = () => {
    onFeedback?.('approve')
  }

  // 处理修改
  const handleModify = () => {
    if (isModifying) {
      // 提交修改
      if (newTaskList.trim()) {
        try {
          // 验证JSON格式
          JSON.parse(newTaskList)
          onFeedback?.(newTaskList.trim())
          setIsModifying(false)
          setNewTaskList('')
        } catch (error) {
          alert('请输入有效的JSON格式任务列表')
        }
      } else {
        alert('请输入任务列表')
      }
    } else {
      // 切换到修改模式
      setIsModifying(true)
      // 预填充当前任务JSON
      if (taskJSON) {
        try {
          const formatted = JSON.stringify(JSON.parse(taskJSON), null, 2)
          setNewTaskList(formatted)
        } catch (error) {
          setNewTaskList(taskJSON || '')
        }
      }
    }
  }

  const handleCancel = () => {
    setIsModifying(false)
    setNewTaskList('')
  }

  return (
    <Card className='w-full bg-blue-50 border-blue-200'>
      <CardHeader className='pb-3'>
        <CardTitle className='flex items-center gap-2 text-sm'>
          <ClipboardList className='h-4 w-4 text-blue-600' />
          任务规划审核
        </CardTitle>
      </CardHeader>
      <CardContent className='pt-0'>
        <p className='text-sm text-gray-700 mb-4'>{message.metadata?.question || message.content}</p>

        {/* 任务统计信息 */}
        {summary && (
          <div className='mb-4 p-3 bg-white/60 rounded-lg border border-blue-100'>
            <h4 className='font-medium text-sm mb-2'>任务统计</h4>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-2 text-xs'>
              <div>
                总任务: <span className='font-medium'>{summary.totalTasks}</span>
              </div>
              <div>
                前端: <span className='font-medium'>{summary.frontendTasks}</span>
              </div>
              <div>
                后端: <span className='font-medium'>{summary.backendTasks}</span>
              </div>
              <div>
                高优先级: <span className='font-medium'>{summary.highPriorityTasks}</span>
              </div>
            </div>
          </div>
        )}

        {/* 任务分解显示 */}
        {taskBreakdown && (
          <div className='mb-4 p-3 bg-white/60 rounded-lg border border-blue-100'>
            <h4 className='font-medium text-sm mb-2'>任务分解</h4>
            <div className='space-y-3'>
              {taskBreakdown.frontend && taskBreakdown.frontend.length > 0 && (
                <div>
                  <h5 className='text-xs font-medium text-green-700 mb-1'>前端任务</h5>
                  <div className='space-y-1'>
                    {taskBreakdown.frontend.map((task, index) => (
                      <div key={index} className='text-xs text-gray-600 flex items-center gap-2'>
                        <span
                          className={`px-1.5 py-0.5 rounded text-xs ${
                            task.priority === 'high'
                              ? 'bg-red-100 text-red-700'
                              : task.priority === 'medium'
                              ? 'bg-yellow-100 text-yellow-700'
                              : 'bg-green-100 text-green-700'
                          }`}
                        >
                          {task.priority}
                        </span>
                        {task.title}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {taskBreakdown.backend && taskBreakdown.backend.length > 0 && (
                <div>
                  <h5 className='text-xs font-medium text-blue-700 mb-1'>后端任务</h5>
                  <div className='space-y-1'>
                    {taskBreakdown.backend.map((task, index) => (
                      <div key={index} className='text-xs text-gray-600 flex items-center gap-2'>
                        <span
                          className={`px-1.5 py-0.5 rounded text-xs ${
                            task.priority === 'high'
                              ? 'bg-red-100 text-red-700'
                              : task.priority === 'medium'
                              ? 'bg-yellow-100 text-yellow-700'
                              : 'bg-green-100 text-green-700'
                          }`}
                        >
                          {task.priority}
                        </span>
                        {task.title}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 修改模式 */}
        {isModifying && (
          <div className='mb-4'>
            <label className='block text-sm font-medium text-gray-700 mb-2'>新的任务列表 (JSON格式)</label>
            <textarea
              value={newTaskList}
              onChange={(e) => setNewTaskList(e.target.value)}
              placeholder='请输入新的任务列表JSON...'
              className='w-full h-40 p-3 border border-gray-300 rounded-md text-sm font-mono'
              style={{ fontSize: '12px' }}
            />
            <p className='text-xs text-gray-500 mt-1'>请确保输入有效的JSON格式任务数组</p>
          </div>
        )}

        {/* 操作按钮 */}
        <div className='flex flex-wrap gap-2'>
          {!isModifying ? (
            <>
              <Button onClick={handleApprove} className='bg-green-600 hover:bg-green-700 text-white' size='sm'>
                <CheckCircle className='h-4 w-4 mr-1' />
                批准继续
              </Button>
              <Button onClick={handleModify} variant='outline' size='sm'>
                <Edit3 className='h-4 w-4 mr-1' />
                修改任务
              </Button>
            </>
          ) : (
            <>
              <Button onClick={handleModify} className='bg-blue-600 hover:bg-blue-700 text-white' size='sm'>
                <Save className='h-4 w-4 mr-1' />
                提交修改
              </Button>
              <Button onClick={handleCancel} variant='outline' size='sm'>
                <X className='h-4 w-4 mr-1' />
                取消
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// 任务完成卡片
export function TaskCompleteCard({ message }: { message: Message }) {
  const taskComplete = message.metadata?.taskComplete

  if (!taskComplete) return null

  return (
    <Card className='w-full bg-green-50 border-green-200'>
      <CardHeader className='pb-3'>
        <CardTitle className='flex items-center gap-2 text-sm'>
          <CheckCircle className='h-4 w-4 text-green-600' />
          任务规划完成
        </CardTitle>
      </CardHeader>
      <CardContent className='pt-0'>
        <p className='text-sm text-green-800 mb-4'>{taskComplete.message}</p>

        {taskComplete.tasks && taskComplete.tasks.length > 0 && (
          <div className='mb-4 p-3 bg-white/60 rounded-lg border border-green-100'>
            <h4 className='font-medium text-sm mb-2'>已生成任务</h4>
            <div className='space-y-1 max-h-32 overflow-y-auto'>
              {taskComplete.tasks.map((task, index) => (
                <div key={task.id || index} className='text-xs text-green-700 flex items-center gap-2'>
                  <span
                    className={`px-1.5 py-0.5 rounded text-xs ${
                      task.type === 'frontend'
                        ? 'bg-green-100 text-green-700'
                        : task.type === 'backend'
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-gray-100 text-gray-700'
                    }`}
                  >
                    {task.type}
                  </span>
                  <span
                    className={`px-1.5 py-0.5 rounded text-xs ${
                      task.priority >= 3
                        ? 'bg-red-100 text-red-700'
                        : task.priority >= 2
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-green-100 text-green-700'
                    }`}
                  >
                    P{task.priority}
                  </span>
                  {task.title}
                </div>
              ))}
            </div>
          </div>
        )}

        <div className='text-xs text-gray-500'>完成时间: {new Date(taskComplete.timestamp).toLocaleString()}</div>
      </CardContent>
    </Card>
  )
}

// 工作流结束卡片
export function EndCard({ message }: { message: Message }) {
  const endInfo = message.metadata?.endInfo

  if (!endInfo) return null

  const getReasonIcon = (reason: string) => {
    switch (reason) {
      case 'completed':
        return <CheckCircle className='h-5 w-5 text-green-600' />
      case 'cancelled':
        return <XCircle className='h-5 w-5 text-gray-600' />
      case 'error':
        return <XCircle className='h-5 w-5 text-red-600' />
      case 'timeout':
        return <AlertCircle className='h-5 w-5 text-yellow-600' />
      default:
        return <CheckCircle className='h-5 w-5 text-gray-600' />
    }
  }

  const getReasonColor = (reason: string) => {
    switch (reason) {
      case 'completed':
        return 'bg-green-50 border-green-200'
      case 'cancelled':
        return 'bg-gray-50 border-gray-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'timeout':
        return 'bg-yellow-50 border-yellow-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  const getReasonTitle = (reason: string) => {
    switch (reason) {
      case 'completed':
        return '工作流已完成'
      case 'cancelled':
        return '工作流已取消'
      case 'error':
        return '工作流遇到错误'
      case 'timeout':
        return '工作流超时'
      default:
        return '工作流结束'
    }
  }

  return (
    <Card className={`w-full ${getReasonColor(endInfo.reason)}`}>
      <CardHeader className='pb-3'>
        <CardTitle className='flex items-center gap-2 text-sm'>
          {getReasonIcon(endInfo.reason)}
          {getReasonTitle(endInfo.reason)}
        </CardTitle>
      </CardHeader>
      <CardContent className='pt-0'>
        {endInfo.summary && <p className='text-sm mb-4 leading-relaxed'>{endInfo.summary}</p>}

        {endInfo.results && (
          <div className='mb-4 p-3 bg-white/60 rounded-lg border border-gray-100'>
            <h4 className='font-medium text-sm mb-2'>结果统计</h4>
            <div className='grid grid-cols-2 md:grid-cols-3 gap-2 text-xs'>
              {endInfo.results.totalTasks && (
                <div className='flex items-center gap-1'>
                  <span className='text-gray-600'>总任务:</span>
                  <span className='font-medium'>{endInfo.results.totalTasks}</span>
                </div>
              )}
              {endInfo.results.frontendTasks !== undefined && (
                <div className='flex items-center gap-1'>
                  <span className='text-gray-600'>前端:</span>
                  <span className='font-medium'>{endInfo.results.frontendTasks}</span>
                </div>
              )}
              {endInfo.results.backendTasks !== undefined && (
                <div className='flex items-center gap-1'>
                  <span className='text-gray-600'>后端:</span>
                  <span className='font-medium'>{endInfo.results.backendTasks}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {endInfo.nextActions && endInfo.nextActions.length > 0 && (
          <div className='mb-4 p-3 bg-white/60 rounded-lg border border-gray-100'>
            <h4 className='font-medium text-sm mb-2'>下一步操作</h4>
            <ul className='text-xs space-y-1'>
              {endInfo.nextActions.map((action, index) => (
                <li key={index} className='flex items-center gap-2'>
                  <ArrowRight className='h-3 w-3 text-gray-400' />
                  <span>{action}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className='text-xs text-gray-500'>完成时间: {new Date().toLocaleString()}</div>
      </CardContent>
    </Card>
  )
}
