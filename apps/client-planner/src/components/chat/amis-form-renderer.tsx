import React, { useState } from 'react';
import { render as renderAmis } from 'amis';
import { useToast } from '@/hooks/use-toast';
import "amis/lib/themes/cxd.css"
interface AmisFormProps {
  formConfig: any;
  onSubmit: (formData: any) => void;
}

// 定义 AMIS fetcherResult 类型
interface FetcherResult {
  data: any;
  status: number;
  headers?: Record<string, string>;
}

export const AmisFormRenderer: React.FC<AmisFormProps> = ({ formConfig, onSubmit }) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // AMIS 环境配置
  const amisEnv = {
    fetcher: ({ url, method, data, config, headers }: any): Promise<FetcherResult> => {
      // 本地表单处理，返回符合 fetcherResult 类型的数据
      return Promise.resolve({
        data: {},
        status: 200 // 添加必需的 status 字段
      });
    },
    isCancel: () => false,
    notify: (type: string, msg: string) => {
      toast({
        title: type === 'error' ? '错误' : '提示',
        description: msg,
        variant: type === 'error' ? 'destructive' : 'default',
      });
    },
    alert: (msg: string) => {
      toast({
        title: '提示',
        description: msg,
      });
    },
    confirm: (msg: string) => Promise.resolve(true),
  };

  // 处理表单提交
  const handleSubmit = (values: any) => {
    setIsSubmitting(true);
    try {
      onSubmit(values);
    } catch (error) {
      console.error('表单提交错误:', error);
      toast({
        title: '提交失败',
        description: '表单提交过程中发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 扩展表单配置
  const extendedConfig = {
    ...formConfig,
    submitText: formConfig.submitText || '提交',
    disabled: isSubmitting,
    onSubmit: handleSubmit,
  };

  return (
    <div className="amis-form-container border rounded-lg p-4 bg-white shadow-sm">
      {renderAmis(extendedConfig, {}, amisEnv)}
    </div>
  );
};
