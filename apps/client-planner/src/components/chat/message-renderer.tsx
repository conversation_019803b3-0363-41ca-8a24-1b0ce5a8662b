import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import type { Message } from '@/service/messages/types'
import { motion } from 'framer-motion'
import { RefreshCw } from 'lucide-react'
import React from 'react'
import ReactMarkdown from 'react-markdown'
import { Prism as Syntax<PERSON><PERSON>lighter } from 'react-syntax-highlighter'
import { atomDark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import remarkGfm from 'remark-gfm'

// 导入 AMIS 表单渲染器
import { AmisFormRenderer } from './amis-form-renderer'
import {
  EndCard,
  ErrorCard,
  InterruptCard,
  TaskCompleteCard,
} from './message-cards'
import { StreamingXMLMessageParser, XMLMessageParser, isXMLContent } from './xml-message-parser'

// 定义 react-markdown code 组件的属性类型
interface MarkdownCodeProps {
  node?: any // HAST AST node
  inline?: boolean
  className?: string
  children?: React.ReactNode // children 是可选的
}

interface MessageRendererProps {
  message: Message
  onFeedback?: (feedback: string) => void
  onFormSubmit?: (formData: any) => void // 添加表单提交回调
  className?: string
}

export function MessageRenderer({
  message,
  onFeedback,
  onFormSubmit, // 接收 onFormSubmit
  className,
}: MessageRendererProps) {
  // 根据消息元数据决定渲染哪种卡片
  const renderMessageCard = () => {
    // 错误消息优先级最高
    if (message.metadata?.error) {
      return <ErrorCard message={message} />
    }

    // 中断消息（需要用户反馈）
    if (message.options && message.options.length > 0) {
      return <InterruptCard message={message} onFeedback={onFeedback} />
    }

    // 任务完成消息
    if (message.metadata?.taskComplete) {
      return <TaskCompleteCard message={message} />
    }

    // 工作流结束消息
    if (message.metadata?.endInfo) {
      return <EndCard message={message} />
    }

    // 默认文本消息
    return <TextMessageCard message={message} onFormSubmit={onFormSubmit} /> // 传递 onFormSubmit
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn('flex w-full', message.role === 'user' ? 'justify-end' : 'justify-start', className)}
    >
      <div className={cn('max-w-[85%]', message.role === 'user' ? 'ml-auto' : 'mr-auto')}>{renderMessageCard()}</div>
    </motion.div>
  )
}

// 默认文本消息卡片
function TextMessageCard({ message, onFormSubmit }: { message: Message; onFormSubmit?: (formData: any) => void }) {
  // 检查消息中是否包含表单配置
  const hasForm = message.metadata && 'formConfig' in message.metadata && message.metadata.formConfig

  // 表单提交处理
  const handleFormSubmit = (formData: any) => {
    if (onFormSubmit) {
      onFormSubmit(formData)
    }
  }

  // 检查消息内容是否包含XML标签（流式处理）
  const isXML = isXMLContent(message.content)

  return (
    <div
      className={cn(
        'rounded-2xl px-3 py-2 shadow-sm',
        message.role === 'user'
          ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
          : 'bg-white border border-gray-200 text-gray-900'
      )}
    >
      <div className='flex flex-col gap-1'>
        {/* Agent 标识 */}
        {message.role === 'assistant' && message.agent && (
          <div className='flex items-center gap-2 text-xs text-gray-500'>
            <AgentIcon agent={message.agent} />
            <span className='capitalize'>{getAgentDisplayName(message.agent)}</span>
          </div>
        )}

        {/* 消息内容 */}
        <div className='text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert prose-p:my-1 prose-ul:my-1 prose-ol:my-1 prose-li:my-0 prose-blockquote:my-1 prose-pre:my-2 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:bg-gray-100 prose-code:dark:bg-gray-800 prose-headings:my-2 break-words'>
          {/* 如果是XML格式，使用XML解析器 */}
          {isXML ? (
            <StreamingXMLMessageParser
              xmlContent={message.content}
              isStreaming={message.isStreaming || false}
              onFormSubmit={(formData) => handleFormSubmit(formData)}
            />
          ) : (
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                // 修复代码块组件
                code({ node, inline, className, children }: MarkdownCodeProps) {
                  const match = /language-(\w+)/.exec(className || '')
                  const codeString = String(children || '').replace(/\n$/, '')

                  if (!inline && match) {
                    // 使用语法高亮渲染
                    return (
                      <div className='my-2'>
                        <SyntaxHighlighter
                          style={atomDark}
                          language={match[1]}
                          PreTag='div'
                          customStyle={{
                            overflowX: 'auto',
                            margin: 0,
                            borderRadius: '0.5rem',
                          }}
                        >
                          {codeString}
                        </SyntaxHighlighter>
                      </div>
                    )
                  } else if (!inline) {
                    // 对于没有语言标识的块级代码，使用简单的深色背景
                    return (
                      <div className='my-2'>
                        <div
                          style={{
                            backgroundColor: '#282c34',
                            color: '#abb2bf',
                            padding: '1rem',
                            borderRadius: '0.5rem',
                            overflowX: 'auto',
                            fontFamily: 'monospace',
                            fontSize: '0.875rem',
                            lineHeight: '1.5',
                          }}
                        >
                          <code>{codeString}</code>
                        </div>
                      </div>
                    )
                  }
                  // 行内代码，使用 prose 默认样式
                  return <code className={className}>{children}</code>
                },
                // 修复段落组件，防止嵌套问题
                p({ children }) {
                  return <div className='my-1'>{children}</div>
                },
                // 链接组件
                a({ node, children, href, ...props }) {
                  if (message.role === 'user') {
                    return <span className='text-blue-200 underline'>{children}</span>
                  }
                  return (
                    <a href={href} {...props} target='_blank' rel='noopener noreferrer'>
                      {children}
                    </a>
                  )
                },
              }}
            >
              {message.content}
            </ReactMarkdown>
          )}
        </div>

        {/* 表单渲染 */}
        {hasForm && message.metadata && (
          <div className='mt-4'>
            <AmisFormRenderer formConfig={message.metadata.formConfig} onSubmit={handleFormSubmit} />
          </div>
        )}

        {/* 工具调用显示 */}
        {message.toolCalls && message.toolCalls.length > 0 && (
          <div className='mt-2 space-y-2'>
            {message.toolCalls.map((toolCall, index) => (
              <ToolCallDisplay key={index} toolCall={toolCall} />
            ))}
          </div>
        )}

        {/* 时间戳和操作按钮 */}
        <div className='flex justify-between items-center mt-1'>
          {message.timestamp && (
            <div className='text-xs opacity-70'>{new Date(message.timestamp).toLocaleTimeString()}</div>
          )}
        </div>
      </div>
    </div>
  )
}

// 工具调用显示组件
function ToolCallDisplay({ toolCall }: { toolCall: any }) {
  return (
    <div className='bg-gray-50 rounded-lg p-3 border border-gray-200'>
      <div className='flex items-center gap-2 mb-2'>
        <div className='w-2 h-2 bg-blue-500 rounded-full' />
        <span className='text-xs font-medium text-gray-700'>调用工具: {toolCall.name}</span>
      </div>

      {toolCall.args && (
        <div className='text-xs text-gray-600 mb-2'>
          <pre className='whitespace-pre-wrap'>{JSON.stringify(toolCall.args, null, 2)}</pre>
        </div>
      )}

      {toolCall.result && (
        <div className='text-xs text-gray-800 bg-white rounded p-2 border'>
          <div className='font-medium mb-1'>结果:</div>
          <div className='whitespace-pre-wrap'>{toolCall.result}</div>
        </div>
      )}
    </div>
  )
}

// Agent 图标组件
function AgentIcon({ agent }: { agent: string }) {
  const iconClass = 'w-3 h-3'

  switch (agent) {
    case 'coordinator':
      return <div className={cn(iconClass, 'bg-blue-500 rounded-full')} />
    case 'planner':
      return <div className={cn(iconClass, 'bg-green-500 rounded-full')} />
    case 'researcher':
      return <div className={cn(iconClass, 'bg-purple-500 rounded-full')} />
    case 'coder':
      return <div className={cn(iconClass, 'bg-orange-500 rounded-full')} />
    case 'analyst':
      return <div className={cn(iconClass, 'bg-pink-500 rounded-full')} />
    case 'reporter':
      return <div className={cn(iconClass, 'bg-indigo-500 rounded-full')} />
    default:
      return <div className={cn(iconClass, 'bg-gray-500 rounded-full')} />
  }
}

// Agent 显示名称
function getAgentDisplayName(agent: string): string {
  const names: Record<string, string> = {
    coordinator: '协调员',
    planner: '规划师',
    researcher: '研究员',
    coder: '开发者',
    analyst: '分析师',
    reporter: '报告员',
  }
  return names[agent] || agent
}
