'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Textarea } from '@/components/ui/textarea'
import { usePlanStore } from '@/store/planStore'
import { cn } from '@/lib/utils'
import { ArrowRight, MessageSquare } from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'

import { TaskDetailModal } from './TaskDetailModal'
// 导入模态框组件
// 删除不需要的TaskStatusInfo导入
import { MessageRenderer } from './message-renderer'

// 导入任务状态类型

interface PlannerChatInterfaceProps {
  sessionId?: string // threadId is managed by the store now
  className?: string
}

export function PlannerChatInterface({
  sessionId,
  className,
}: PlannerChatInterfaceProps) {
  const [input, setInput] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedTask, setSelectedTask] = useState<any | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const {
    messages,
    isLoading,
    waitingForFeedback,
    interruptMessage,
    sendMessage,
    setActiveThread,
    activeThreadId,
  } = usePlanStore()

  useEffect(() => {
    if (sessionId && sessionId !== activeThreadId) {
      setActiveThread(sessionId)
    }
    // Cleanup when component unmounts or sessionId becomes null
    return () => {
      if (sessionId) {
        // Decide if you want to clear the active thread when the chat interface is unmounted
        // setActiveThread(null);
      }
    }
  }, [sessionId, setActiveThread, activeThreadId])

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 处理发送消息
  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return

    const messageContent = input.trim()
    setInput('')

    // 重置 textarea 高度
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }

    await sendMessage(messageContent, waitingForFeedback)
  }

  // 处理查看任务详情
  const handleViewTaskDetails = (task: any) => {
    setSelectedTask(task)
    setIsModalOpen(true)
  }

  // 处理关闭模态框
  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedTask(null)
  }

  // 处理消息反馈
  const handleFeedback = async (feedback: string) => {
    await sendMessage(feedback, true)
  }

  // 处理表单提交
  const handleFormSubmit = async (formData: any) => {
    // 将表单数据转换为 JSON 字符串作为 interruptFeedback
    const formDataJson = JSON.stringify(formData)
    await sendMessage(formDataJson, true)
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 自动调整 textarea 高度
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value)

    // 自动调整高度
    const textarea = e.target
    textarea.style.height = 'auto'
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`
  }

  return (
    <div className={cn('flex-1 flex flex-col h-full chat', className)}>
      {/* 消息列表 */}
      {messages.length === 0 && !isLoading ? (
        <div className='text-center py-12 flex flex-col items-center justify-center flex-1'>
          <div className='w-16 h-16 bg-gradient-to-r from-purple-100 to-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4'>
            <MessageSquare className='h-8 w-8 text-purple-500' />
          </div>
          <h3 className='text-lg font-medium text-gray-900 mb-2'>开始对话</h3>
          <p className='text-gray-600 text-sm max-w-md mx-auto'>输入您的需求，AI 规划助手将帮助您分析和制定计划</p>
        </div>
      ) : (
        <ScrollArea className='flex-1 p-4 messages max-h-[calc(100vh-530px)]'>
          <div className='space-y-4'>
            {messages.map((message) => (
              <MessageRenderer
                key={message.id}
                message={message}
                onFeedback={handleFeedback}
                onFormSubmit={handleFormSubmit} // 传递表单提交回调
              />
            ))}

            {/* 等待反馈提示 */}
            {waitingForFeedback && interruptMessage && (
              <Card className='bg-yellow-50 border-yellow-200'>
                <CardContent className='p-4'>
                  <p className='text-sm text-yellow-800 mb-3'>AI 正在等待您的反馈...</p>
                  <div className='text-xs text-yellow-600'>请填写表单并点击提交</div>
                </CardContent>
              </Card>
            )}

            {/* 加载指示器 */}
            {isLoading && (
              <div className='flex justify-start'>
                <div className='bg-gray-50 border border-gray-200 rounded-2xl p-4 max-w-xs'>
                  <div className='flex items-center gap-2 text-gray-600'>
                    <div className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'></div>
                    <div
                      className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'
                      style={{ animationDelay: '0.1s' }}
                    ></div>
                    <div
                      className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'
                      style={{ animationDelay: '0.2s' }}
                    ></div>
                    <span className='text-sm ml-2'>AI 正在思考...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      )}

      {/* 输入区域 */}
      <div className='p-4 pb-0 border-t border-gray-200 bg-white space-y-4 input-area'>
        <Textarea
          ref={textareaRef}
          value={input}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={
            waitingForFeedback ? '请输入您的反馈... (Shift+Enter 换行)' : '输入您的消息... (Shift+Enter 换行)'
          }
          className='min-h-[140px] max-h-[220px] resize-none'
          disabled={isLoading}
        />
        <div className='flex justify-end'>
          <Button type='submit' disabled={isLoading} className='linear-button' onClick={handleSendMessage}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2'></div>
                发送中...
              </>
            ) : (
              <>
                {waitingForFeedback ? '发送反馈' : '发送消息'}
                <ArrowRight className='h-4 w-4 ml-2' />
              </>
            )}
          </Button>
        </div>
      </div>
      <TaskDetailModal isOpen={isModalOpen} onClose={handleCloseModal} task={selectedTask} />
    </div>
  )
}
