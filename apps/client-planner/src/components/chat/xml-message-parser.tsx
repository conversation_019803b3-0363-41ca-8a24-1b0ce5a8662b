import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, FileText, Download, Eye, Copy } from 'lucide-react';
import { AmisFormRenderer } from './amis-form-renderer';

interface XMLMessageParserProps {
  xmlContent: string;
  onFormSubmit?: (formData: any) => void;
}

interface StreamingXMLMessageParserProps {
  xmlContent: string;
  isStreaming: boolean;
  onFormSubmit?: (formData: any) => void;
}

interface ParsedXMLContent {
  status?: string;
  amisJson?: any;
  fileContent?: {
    name: string;
    filetype: string;
    content: string;
  };
}

/**
 * 解析XML格式的消息内容
 */
function parseXMLContent(xmlContent: string): ParsedXMLContent {
  const result: ParsedXMLContent = {};

  // 解析status标签
  const statusMatch = xmlContent.match(/<status>([\s\S]*?)<\/status>/);
  if (statusMatch) {
    result.status = statusMatch[1].trim();
  }

  // 解析amis_json标签
  const amisJsonMatch = xmlContent.match(/<amis_json>([\s\S]*?)<\/amis_json>/);
  if (amisJsonMatch) {
    try {
      result.amisJson = JSON.parse(amisJsonMatch[1].trim());
    } catch (error) {
      console.error('解析AMIS JSON失败:', error);
      result.amisJson = null;
    }
  }

  // 解析file标签
  const fileMatch = xmlContent.match(/<file\s+name=['"](.*?)['"][^>]*filetype=['"](.*?)['"][^>]*>([\s\S]*?)<\/file>/);
  if (fileMatch) {
    result.fileContent = {
      name: fileMatch[1].trim(),
      filetype: fileMatch[2].trim(),
      content: fileMatch[3].trim(),
    };
  }

  return result;
}

/**
 * 流式解析XML格式的消息内容
 */
function parseStreamingXMLContent(xmlContent: string, isStreaming: boolean): {
  status?: string;
  statusComplete: boolean;
  amisJson?: any;
  amisJsonComplete: boolean;
  amisJsonStarted: boolean;
  fileContent?: {
    name: string;
    filetype: string;
    content: string;
  };
  fileComplete: boolean;
  fileStarted: boolean;
} {
  const result: {
    status?: string;
    statusComplete: boolean;
    amisJson?: any;
    amisJsonComplete: boolean;
    amisJsonStarted: boolean;
    fileContent?: {
      name: string;
      filetype: string;
      content: string;
    };
    fileComplete: boolean;
    fileStarted: boolean;
  } = {
    statusComplete: false,
    amisJsonComplete: false,
    amisJsonStarted: false,
    fileComplete: false,
    fileStarted: false,
  };

  // 解析status标签
  const statusMatch = xmlContent.match(/<status>([\s\S]*?)<\/status>/);
  if (statusMatch) {
    result.status = statusMatch[1].trim();
    result.statusComplete = true;
  } else {
    // 检查是否有未闭合的status标签
    const statusStartMatch = xmlContent.match(/<status>([\s\S]*?)$/);
    if (statusStartMatch) {
      result.status = statusStartMatch[1].trim();
      result.statusComplete = false;
    }
  }

  // 检查amis_json是否开始
  if (xmlContent.includes('<amis_json>')) {
    result.amisJsonStarted = true;

    // 解析amis_json标签
    const amisJsonMatch = xmlContent.match(/<amis_json>([\s\S]*?)<\/amis_json>/);
    if (amisJsonMatch) {
      try {
        result.amisJson = JSON.parse(amisJsonMatch[1].trim());
        result.amisJsonComplete = true;
      } catch (error) {
        console.error('解析AMIS JSON失败:', error);
        result.amisJson = null;
        result.amisJsonComplete = false;
      }
    } else {
      // amis_json开始了但还没结束
      result.amisJsonComplete = false;
    }
  }

  // 检查file是否开始
  if (xmlContent.includes('<file')) {
    result.fileStarted = true;

    // 解析file标签
    const fileMatch = xmlContent.match(/<file\s+name=['"](.*?)['"][^>]*filetype=['"](.*?)['"][^>]*>([\s\S]*?)<\/file>/);
    if (fileMatch) {
      result.fileContent = {
        name: fileMatch[1].trim(),
        filetype: fileMatch[2].trim(),
        content: fileMatch[3].trim(),
      };
      result.fileComplete = true;
    } else {
      // file开始了但还没结束
      result.fileComplete = false;
    }
  }

  return result;
}

/**
 * 流式XML消息解析器组件
 * 支持流式解析XML内容，实时更新显示状态
 */
export function StreamingXMLMessageParser({
  xmlContent,
  isStreaming,
  onFormSubmit
}: StreamingXMLMessageParserProps) {
  const [parsedContent, setParsedContent] = useState<{
    status?: string;
    statusComplete: boolean;
    amisJson?: any;
    amisJsonComplete: boolean;
    amisJsonStarted: boolean;
    fileContent?: {
      name: string;
      filetype: string;
      content: string;
    };
    fileComplete: boolean;
    fileStarted: boolean;
  }>({
    statusComplete: false,
    amisJsonComplete: false,
    amisJsonStarted: false,
    fileComplete: false,
    fileStarted: false,
  });

  const [showAmisForm, setShowAmisForm] = useState(false);

  useEffect(() => {
    const parsed = parseStreamingXMLContent(xmlContent, isStreaming);
    setParsedContent(parsed);

    // 如果AMIS JSON完整且还在流式传输中，显示表单
    if (parsed.amisJsonComplete && !showAmisForm) {
      // 短暂延迟后显示表单，提供更好的用户体验
      const timer = setTimeout(() => {
        setShowAmisForm(true);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [xmlContent, isStreaming, showAmisForm]);

  const handleFormSubmit = (formData: any) => {
    onFormSubmit?.(formData);
  };

  return (
    <div className="space-y-4">
      {/* 状态信息渲染 */}
      {parsedContent.status && (
        <Alert className="border-amber-200 bg-amber-50">
          <AlertDescription className="text-amber-800 whitespace-pre-line">
            {parsedContent.status}
            {!parsedContent.statusComplete && isStreaming && (
              <span className="inline-block w-2 h-4 ml-1 bg-amber-600 animate-pulse" />
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* 文件内容渲染 */}
      {parsedContent.fileStarted && (
        <div className="mt-4">
          {!parsedContent.fileComplete ? (
            <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex items-center space-x-2 text-gray-600">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span className="text-sm">正在接收文件内容...</span>
              </div>
            </div>
          ) : parsedContent.fileContent ? (
            <FileContentRenderer fileContent={parsedContent.fileContent} />
          ) : null}
        </div>
      )}

      {/* AMIS表单渲染 */}
      {parsedContent.amisJsonStarted && (
        <div className="mt-4">
          {!parsedContent.amisJsonComplete || !showAmisForm ? (
            <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex items-center space-x-2 text-gray-600">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span className="text-sm">
                  {!parsedContent.amisJsonComplete
                    ? '正在接收表单数据...'
                    : '正在准备表单...'
                  }
                </span>
              </div>
            </div>
          ) : (
            <AmisFormRenderer
              formConfig={parsedContent.amisJson}
              onSubmit={handleFormSubmit}
            />
          )}
        </div>
      )}
    </div>
  );
}

/**
 * XML消息解析器组件
 * 用于渲染包含status和amis_json标签的XML响应
 */
export function XMLMessageParser({ xmlContent, onFormSubmit }: XMLMessageParserProps) {
  const [parsedContent, setParsedContent] = useState<ParsedXMLContent>({});
  const [isLoadingAmis, setIsLoadingAmis] = useState(false);
  const [showAmisForm, setShowAmisForm] = useState(false);

  useEffect(() => {
    const parsed = parseXMLContent(xmlContent);
    setParsedContent(parsed);

    // 如果有AMIS JSON，显示加载状态然后渲染表单
    if (parsed.amisJson) {
      setIsLoadingAmis(true);
      const timer = setTimeout(() => {
        setIsLoadingAmis(false);
        setShowAmisForm(true);
      }, 800); // 模拟加载时间

      return () => clearTimeout(timer);
    }
  }, [xmlContent]);

  const handleFormSubmit = (formData: any) => {
    onFormSubmit?.(formData);
  };

  return (
    <div className="space-y-4">
      {/* 状态信息渲染 */}
      {parsedContent.status && (
        <Alert className="border-amber-200 bg-amber-50">
          <AlertDescription className="text-amber-800 whitespace-pre-line">
            {parsedContent.status}
          </AlertDescription>
        </Alert>
      )}

      {/* 文件内容渲染 */}
      {parsedContent.fileContent && (
        <FileContentRenderer fileContent={parsedContent.fileContent} />
      )}

      {/* AMIS表单渲染 */}
      {parsedContent.amisJson && (
        <div className="mt-4">
          {isLoadingAmis ? (
            <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex items-center space-x-2 text-gray-600">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span className="text-sm">正在准备表单...</span>
              </div>
            </div>
          ) : showAmisForm ? (
            <AmisFormRenderer
              formConfig={parsedContent.amisJson}
              onSubmit={handleFormSubmit}
            />
          ) : null}
        </div>
      )}
    </div>
  );
}

/**
 * 检查内容是否为XML格式
 */
export function isXMLContent(content: string): boolean {
  return content.includes('<status>') || content.includes('<amis_json>') || content.includes('<file');
}

/**
 * 文件内容展示组件
 */
function FileContentRenderer({ fileContent }: { fileContent: { name: string; filetype: string; content: string } }) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(fileContent.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const handleDownload = () => {
    const blob = new Blob([fileContent.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileContent.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getFileTypeIcon = (filetype: string) => {
    switch (filetype.toLowerCase()) {
      case 'xml':
      case 'html':
      case 'json':
      case 'txt':
        return <FileText className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  return (
    <Card className="mt-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getFileTypeIcon(fileContent.filetype)}
            <CardTitle className="text-base font-medium">
              {fileContent.name}
            </CardTitle>
            <span className="text-xs px-2 py-1 bg-gray-100 rounded text-gray-600">
              {fileContent.filetype.toUpperCase()}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopy}
              className="h-8"
            >
              <Copy className="h-3 w-3 mr-1" />
              {copied ? '已复制' : '复制'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
              className="h-8"
            >
              <Download className="h-3 w-3 mr-1" />
              下载
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="h-8">
                  <Eye className="h-3 w-3 mr-1" />
                  查看
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl h-[80vh]">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    {getFileTypeIcon(fileContent.filetype)}
                    {fileContent.name}
                  </DialogTitle>
                </DialogHeader>
                <ScrollArea className="h-full mt-4">
                  <pre className="bg-gray-50 p-4 rounded-lg text-sm font-mono whitespace-pre-wrap overflow-auto">
                    {fileContent.content}
                  </pre>
                </ScrollArea>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="bg-gray-50 rounded-lg p-4 max-h-40 overflow-hidden relative">
          <pre className="text-sm font-mono whitespace-pre-wrap text-gray-700">
            {fileContent.content.length > 200
              ? fileContent.content.substring(0, 200) + '...'
              : fileContent.content}
          </pre>
          {fileContent.content.length > 200 && (
            <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-gray-50 to-transparent" />
          )}
        </div>
        <div className="mt-2 text-xs text-gray-500">
          文件大小: {(fileContent.content.length / 1024).toFixed(2)} KB
        </div>
      </CardContent>
    </Card>
  );
}
