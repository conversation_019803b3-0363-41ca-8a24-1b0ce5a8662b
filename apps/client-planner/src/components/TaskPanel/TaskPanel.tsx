import React, { useState } from 'react';
import { usePlanStore } from '../../store/planStore';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select';
import {
  CheckCircle2,
  Circle,
  Clock,
  Plus,
  AlertCircle,
  Trash2,
  ExternalLink
} from 'lucide-react';
import type { Task } from '../../service/api/plan-types';

// 任务状态映射
const taskStatusMap = {
  pending: { label: '待处理', icon: Circle, color: 'text-gray-500' },
  in_progress: { label: '进行中', icon: Clock, color: 'text-blue-500' },
  completed: { label: '已完成', icon: CheckCircle2, color: 'text-green-500' },
  deleted: { label: '已删除', icon: AlertCircle, color: 'text-red-500' }
};

// 任务类型映射
const taskTypeMap = {
  frontend: { label: '前端', color: 'bg-blue-100 text-blue-800' },
  backend: { label: '后端', color: 'bg-green-100 text-green-800' },
  integration: { label: '集成', color: 'bg-purple-100 text-purple-800' }
};

interface CreateTaskDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  threadId: string;
}

const CreateTaskDialog: React.FC<CreateTaskDialogProps> = ({
  open,
  onOpenChange,
  threadId
}) => {
  const { createTask } = usePlanStore();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'frontend' as 'frontend' | 'backend' | 'integration',
    priority: 0
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim()) return;

    setLoading(true);
    try {
      await createTask({
        threadId: threadId,
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        type: formData.type,
        priority: formData.priority
      });

      setFormData({
        title: '',
        description: '',
        type: 'frontend',
        priority: 0
      });
      onOpenChange(false);
    } catch (error) {
      console.error('创建任务失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>创建新任务</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">任务标题</label>
            <Input
              placeholder="输入任务标题..."
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              autoFocus
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">任务描述</label>
            <Textarea
              placeholder="输入任务描述..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">任务类型</label>
              <Select
                value={formData.type}
                onValueChange={(value: 'frontend' | 'backend' | 'integration') =>
                  setFormData(prev => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="frontend">前端</SelectItem>
                  <SelectItem value="backend">后端</SelectItem>
                  <SelectItem value="integration">集成</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">优先级</label>
              <Select
                value={formData.priority.toString()}
                onValueChange={(value) =>
                  setFormData(prev => ({ ...prev, priority: parseInt(value) }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">普通</SelectItem>
                  <SelectItem value="1">高</SelectItem>
                  <SelectItem value="2">紧急</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={!formData.title.trim() || loading}
            >
              {loading ? '创建中...' : '创建'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

interface TaskItemProps {
  task: Task;
  onStatusChange: (taskId: string, status: Task['status']) => void;
  onDelete: (taskId: string) => void;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, onStatusChange, onDelete }) => {
  const statusInfo = taskStatusMap[task.status];
  const typeInfo = taskTypeMap[task.type];
  const StatusIcon = statusInfo.icon;

  return (
    <div className="p-4 border rounded-lg bg-white hover:shadow-sm transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-2">
            <button
              onClick={() => {
                if (task.status === 'completed') {
                  onStatusChange(task.id, 'pending');
                } else if (task.status === 'pending') {
                  onStatusChange(task.id, 'in_progress');
                } else if (task.status === 'in_progress') {
                  onStatusChange(task.id, 'completed');
                }
              }}
              className={`${statusInfo.color} hover:opacity-70 transition-opacity`}
            >
              <StatusIcon className="h-5 w-5" />
            </button>

            <h4 className="text-sm font-medium text-gray-900 truncate">
              {task.title}
            </h4>

            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${typeInfo.color}`}>
              {typeInfo.label}
            </span>
          </div>

          {task.description && (
            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
              {task.description}
            </p>
          )}

          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span className={statusInfo.color}>
              {statusInfo.label}
            </span>

            {task.progress > 0 && (
              <span>进度: {task.progress}%</span>
            )}

            {task.priority > 0 && (
              <span className="text-orange-500">
                {task.priority === 1 ? '高优先级' : '紧急'}
              </span>
            )}
          </div>

          {task.deployment_url && (
            <div className="mt-2">
              <a
                href={task.deployment_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800"
              >
                <ExternalLink className="h-3 w-3" />
                <span>查看部署</span>
              </a>
            </div>
          )}
        </div>

        <div className="flex space-x-1 ml-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => onDelete(task.id)}
            className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {task.progress > 0 && task.status === 'in_progress' && (
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${task.progress}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

const TaskPanel: React.FC = () => {
  const {
    activeThread,
    tasks,
    tasksLoading,
    tasksError,
    taskProgress,
    updateTaskStatus,
    deleteTask
  } = usePlanStore();

  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');

  if (!activeThread) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">请选择一个会话来查看任务</p>
        </div>
      </div>
    );
  }

  const handleStatusChange = async (taskId: string, status: Task['status']) => {
    await updateTaskStatus(taskId, { status });
  };

  const handleDeleteTask = async (taskId: string) => {
    if (confirm('确定要删除这个任务吗？')) {
      await deleteTask(taskId);
    }
  };

  const filteredTasks = tasks.filter(task => {
    if (filterStatus === 'all') return task.status !== 'deleted';
    return task.status === filterStatus;
  });

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">任务管理</h2>
          <Button
            size="sm"
            onClick={() => setCreateDialogOpen(true)}
            className="flex items-center space-x-1"
          >
            <Plus className="h-4 w-4" />
            <span>新建任务</span>
          </Button>
        </div>

        {/* 任务进度 */}
        {taskProgress && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">总体进度</span>
              <span className="text-sm text-gray-600">
                {taskProgress.completed} / {taskProgress.total} 已完成
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${taskProgress.total > 0 ? (taskProgress.completed / taskProgress.total) * 100 : 0}%`
                }}
              />
            </div>
          </div>
        )}

        {/* 筛选器 */}
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-full">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部任务</SelectItem>
            <SelectItem value="pending">待处理</SelectItem>
            <SelectItem value="in_progress">进行中</SelectItem>
            <SelectItem value="completed">已完成</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 任务列表 */}
      <div className="flex-1 overflow-y-auto p-4">
        {tasksLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : tasksError ? (
          <div className="text-center py-8">
            <p className="text-red-500">{tasksError}</p>
          </div>
        ) : filteredTasks.length === 0 ? (
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">
              {filterStatus === 'all' ? '还没有任何任务' : `没有${taskStatusMap[filterStatus as keyof typeof taskStatusMap]?.label}的任务`}
            </p>
            {filterStatus === 'all' && (
              <Button onClick={() => setCreateDialogOpen(true)}>
                创建第一个任务
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {filteredTasks.map((task) => (
              <TaskItem
                key={task.id}
                task={task}
                onStatusChange={handleStatusChange}
                onDelete={handleDeleteTask}
              />
            ))}
          </div>
        )}
      </div>

      {/* 创建任务对话框 */}
      <CreateTaskDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        threadId={activeThread.id}
      />
    </div>
  );
};

export default TaskPanel;
