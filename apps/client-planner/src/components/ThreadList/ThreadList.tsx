import { Archive, Calendar, CheckCircle, Clock, MessageSquare, Plus, Trash2 } from 'lucide-react'
import React, { useEffect, useState } from 'react'

import { usePlanStore } from '../../store/planStore'
import { getTaskStats, getThreadStatus } from '../../utils/threadStatus'
import { Button } from '../ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog'
import { Input } from '../ui/input'

// 简单的时间格式化函数
const formatTimeAgo = (date: Date) => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 30) return `${diffDays}天前`
  return date.toLocaleDateString('zh-CN')
}

interface CreateThreadDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateThread: (title: string) => void
  loading: boolean
}

const CreateThreadDialog: React.FC<CreateThreadDialogProps> = ({ open, onOpenChange, onCreateThread, loading }) => {
  const [title, setTitle] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (title.trim()) {
      onCreateThread(title.trim())
      setTitle('')
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>创建新会话</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className='space-y-4'>
          <div>
            <Input placeholder='输入会话标题...' value={title} onChange={(e) => setTitle(e.target.value)} autoFocus />
          </div>
          <div className='flex justify-end space-x-2'>
            <Button type='button' variant='outline' onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type='submit' disabled={!title.trim() || loading}>
              {loading ? '创建中...' : '创建'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

interface ThreadItemProps {
  thread: any
  isActive: boolean
  onSelect: () => void
  onArchive: () => void
  onDelete: () => void
}

const ThreadItem: React.FC<ThreadItemProps> = ({ thread, isActive, onSelect, onArchive, onDelete }) => {
  const status = getThreadStatus(thread)
  const StatusIcon = status.icon

  return (
    <div
      className={`
        p-4 border rounded-lg cursor-pointer transition-all duration-200
        ${isActive ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'}
      `}
      onClick={onSelect}
    >
      <div className='flex items-start justify-between'>
        <div className='flex-1 min-w-0'>
          <div className='flex items-center space-x-2 mb-2'>
            <MessageSquare className='h-4 w-4 text-gray-500' />
            <h3 className='text-sm font-medium text-gray-900 truncate'>{thread.title}</h3>
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${status.bgColor}`}>
              <StatusIcon className={`h-3 w-3 ${status.color}`} />
              <span className={status.color}>{status.label}</span>
            </div>
          </div>

          <div className='flex items-center space-x-4 text-xs text-gray-500 mb-2'>
            <div className='flex items-center space-x-1'>
              <Calendar className='h-3 w-3' />
              <span>{formatTimeAgo(new Date(thread.created_at))}</span>
            </div>
            <span>{status.description}</span>
          </div>

          {/* 任务统计 */}
          {(() => {
            const taskStats = getTaskStats(thread.tasks)
            if (!taskStats) return null

            return (
              <div className='flex items-center space-x-2 text-xs'>
                <div className='flex items-center space-x-1'>
                  <CheckCircle className='h-3 w-3 text-green-500' />
                  <span className='text-green-700'>
                    {taskStats.completed}/{taskStats.total} 完成
                  </span>
                </div>
                {(taskStats.pending > 0 || taskStats.inProgress > 0) && (
                  <>
                    <div className='text-gray-400'>|</div>
                    <div className='flex items-center space-x-1'>
                      <Clock className='h-3 w-3 text-blue-500' />
                      <span className='text-blue-700'>
                        {taskStats.inProgress > 0 ? `${taskStats.inProgress} 进行中` : `${taskStats.pending} 待处理`}
                      </span>
                    </div>
                  </>
                )}
              </div>
            )
          })()}

          {thread.prdLink && (
            <div className='mt-2'>
              <span className='inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800'>
                已提交PRD
              </span>
            </div>
          )}
        </div>

        <div className='flex space-x-1 ml-2'>
          <Button
            size='sm'
            variant='ghost'
            onClick={(e) => {
              e.stopPropagation()
              onArchive()
            }}
            className='h-6 w-6 p-0'
          >
            <Archive className='h-3 w-3' />
          </Button>
          <Button
            size='sm'
            variant='ghost'
            onClick={(e) => {
              e.stopPropagation()
              onDelete()
            }}
            className='h-6 w-6 p-0 text-red-500 hover:text-red-700'
          >
            <Trash2 className='h-3 w-3' />
          </Button>
        </div>
      </div>
    </div>
  )
}

const ThreadList: React.FC = () => {
  const {
    threads,
    activeThread,
    threadsLoading,
    threadsError,
    fetchThreads,
    createThread,
    setActiveThread,
    archiveThread,
    deleteThread,
  } = usePlanStore()

  const [createDialogOpen, setCreateDialogOpen] = useState(false)

  useEffect(() => {
    fetchThreads()
  }, [fetchThreads])

  const handleCreateThread = async (title: string) => {
    const newThread = await createThread({ title })
    if (newThread) {
      setActiveThread(newThread.id)
    }
  }

  const handleArchiveThread = async (threadId: string) => {
    if (confirm('确定要归档这个会话吗？')) {
      await archiveThread(threadId)
    }
  }

  const handleDeleteThread = async (threadId: string) => {
    if (confirm('确定要删除这个会话吗？此操作无法撤销。')) {
      await deleteThread(threadId)
    }
  }

  if (threadsLoading && threads.length === 0) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-500'>加载会话列表...</p>
        </div>
      </div>
    )
  }

  if (threadsError) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <p className='text-red-500 mb-4'>{threadsError}</p>
          <Button onClick={() => fetchThreads()}>重试</Button>
        </div>
      </div>
    )
  }

  return (
    <div className='h-full flex flex-col'>
      {/* 线程列表 */}
      <div className='flex-1 overflow-y-auto'>
        {threads.length === 0 ? (
          <div className='text-center py-12'>
            <MessageSquare className='h-12 w-12 text-gray-300 mx-auto mb-4' />
            <p className='text-gray-500 mb-4'>还没有任何会话</p>
          </div>
        ) : (
          <div className='space-y-3'>
            {threads.map((thread) => (
              <ThreadItem
                key={thread.id}
                thread={thread}
                isActive={activeThread?.id === thread.id}
                onSelect={() => setActiveThread(thread.id)}
                onArchive={() => handleArchiveThread(thread.id)}
                onDelete={() => handleDeleteThread(thread.id)}
              />
            ))}
          </div>
        )}
      </div>

      {/* 创建对话框 */}
      <CreateThreadDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onCreateThread={handleCreateThread}
        loading={threadsLoading}
      />
    </div>
  )
}

export default ThreadList
