import { CheckCircle, Clock } from 'lucide-react'

// 获取会话状态信息
export const getThreadStatus = (thread: any) => {
  const taskCount = Array.isArray(thread.tasks) ? thread.tasks.length : 0
  const messageCount = Array.isArray(thread.messages) ? thread.messages.length : 0

  if (taskCount > 0) {
    // 检查任务完成情况
    const completedTasks = thread.tasks.filter((t: any) => t.status === 'completed').length
    const pendingTasks = thread.tasks.filter((t: any) => t.status === 'pending').length

    if (completedTasks === taskCount) {
      return {
        type: 'all_completed',
        label: '全部完成',
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        description: `${taskCount} 个任务全部完成`,
      }
    } else {
      return {
        type: 'has_tasks',
        label: '任务已生成',
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        description: `共 ${taskCount} 个任务`,
      }
    }
  } else {
    // 合并新会话和对话进行中状态
    return {
      type: 'in_progress',
      label: '对话进行中',
      icon: Clock,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: messageCount > 0 ? `${messageCount} 条消息` : '尚未开始对话',
    }
  }
}

// 获取任务统计信息
export const getTaskStats = (tasks: any[]) => {
  if (!Array.isArray(tasks) || tasks.length === 0) {
    return null
  }

  const completedTasks = tasks.filter((t: any) => t.status === 'completed').length
  const pendingTasks = tasks.filter((t: any) => t.status === 'pending').length
  const inProgressTasks = tasks.filter((t: any) => t.status === 'in_progress').length

  return {
    total: tasks.length,
    completed: completedTasks,
    pending: pendingTasks,
    inProgress: inProgressTasks,
  }
}
