/**
 * 日期处理工具函数
 */

/**
 * 安全地将各种时间格式转换为ISO字符串
 * @param dateInput - 可能是Date对象、字符串或其他格式
 * @returns ISO格式的时间字符串
 */
export const toISOString = (dateInput: Date | string | number | null | undefined): string => {
  if (!dateInput) {
    return new Date().toISOString();
  }

  // 如果已经是ISO格式的字符串，直接返回
  if (typeof dateInput === 'string' && dateInput.includes('T') && dateInput.includes('Z')) {
    return dateInput;
  }

  // 如果是字符串，尝试解析
  if (typeof dateInput === 'string') {
    try {
      return new Date(dateInput).toISOString();
    } catch {
      return new Date().toISOString();
    }
  }

  // 如果是Date对象
  if (dateInput instanceof Date) {
    return dateInput.toISOString();
  }

  // 如果是数字（时间戳）
  if (typeof dateInput === 'number') {
    return new Date(dateInput).toISOString();
  }

  // 其他情况，尝试转换
  try {
    return new Date(dateInput as any).toISOString();
  } catch {
    return new Date().toISOString();
  }
};

/**
 * 格式化时间为本地化字符串
 * @param dateInput - 时间输入
 * @param options - 格式化选项
 * @returns 格式化后的时间字符串
 */
export const formatTime = (
  dateInput: Date | string | number | null | undefined,
  options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit'
  }
): string => {
  if (!dateInput) {
    return new Date().toLocaleTimeString('zh-CN', options);
  }

  try {
    const date = typeof dateInput === 'string' || typeof dateInput === 'number'
      ? new Date(dateInput)
      : dateInput;

    return date.toLocaleTimeString('zh-CN', options);
  } catch {
    return new Date().toLocaleTimeString('zh-CN', options);
  }
};

/**
 * 格式化日期为本地化字符串
 * @param dateInput - 日期输入
 * @param options - 格式化选项
 * @returns 格式化后的日期字符串
 */
export const formatDate = (
  dateInput: Date | string | number | null | undefined,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }
): string => {
  if (!dateInput) {
    return new Date().toLocaleDateString('zh-CN', options);
  }

  try {
    const date = typeof dateInput === 'string' || typeof dateInput === 'number'
      ? new Date(dateInput)
      : dateInput;

    return date.toLocaleDateString('zh-CN', options);
  } catch {
    return new Date().toLocaleDateString('zh-CN', options);
  }
};

/**
 * 格式化完整的日期时间
 * @param dateInput - 日期时间输入
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (
  dateInput: Date | string | number | null | undefined
): string => {
  if (!dateInput) {
    return new Date().toLocaleString('zh-CN');
  }

  try {
    const date = typeof dateInput === 'string' || typeof dateInput === 'number'
      ? new Date(dateInput)
      : dateInput;

    return date.toLocaleString('zh-CN');
  } catch {
    return new Date().toLocaleString('zh-CN');
  }
};

/**
 * 计算相对时间（如：3分钟前，1小时前）
 * @param dateInput - 日期输入
 * @returns 相对时间字符串
 */
export const getRelativeTime = (
  dateInput: Date | string | number | null | undefined
): string => {
  if (!dateInput) {
    return '刚刚';
  }

  try {
    const date = typeof dateInput === 'string' || typeof dateInput === 'number'
      ? new Date(dateInput)
      : dateInput;

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
      return '刚刚';
    } else if (diffMins < 60) {
      return `${diffMins}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 30) {
      return `${diffDays}天前`;
    } else {
      return formatDate(date, { month: 'short', day: 'numeric' });
    }
  } catch {
    return '刚刚';
  }
};
