// apps/client-planner/src/service/api/plan-types.ts
// 与后端API对应的前端类型定义

// ===================== 基础类型 =====================

export interface ApiResponse<T = any> {
  data?: T
  message?: string
  error?: string
}

export interface PaginationParams {
  page?: number
  limit?: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
}

// ===================== Thread 相关类型 =====================

export interface Thread {
  id: string
  user_id?: string
  title: string
  prdLink?: string
  status: number
  messages: any[] // 关联字段：来自messages表
  state?: Record<string, any>
  tasks: any[] // 关联字段：来自tasks表
  created_at: Date | string
  updated_at: Date | string
  interrupt_status?: number
  interrupt_data?: Record<string, any>
  interrupt_at?: Date | string
}

export enum InterruptStatus {
  NONE = 0,
  WAITING_FEEDBACK = 1,
  FEEDBACK_RECEIVED = 2,
}

export interface InterruptInfo {
  threadId: string
  interruptStatus: InterruptStatus
  interruptData: Record<string, any> | null
  interruptAt: Date | string | null
  isWaitingFeedback: boolean
}

export interface CreateThreadRequest {
  title: string
  user_id?: string
}

export interface UpdateThreadRequest {
  title?: string
  prdLink?: string
  status?: number
}

export interface GetThreadsRequest extends PaginationParams {
  user_id?: string
  status?: number
}

export interface StartPlanningRequest {
  threadId: string
  prd_content: string
}

export interface SubmitPRDRequest {
  threadId: string
  prd_link: string
}

// ===================== Message 相关类型 =====================

export interface Message {
  id: string
  threadId: string
  type: 'user' | 'assistant' | 'system' | 'interrupt' | 'tools'
  content: string
  agent?: 'coordinator' | 'planner' | 'researcher' | 'coder' | 'analyst' | 'reporter'
  status: number
  metadata?: Record<string, any>
  created_at: Date | string
  updated_at: Date | string
}

export interface CreateMessageRequest {
  threadId: string
  type: 'user' | 'assistant' | 'system' | 'interrupt' | 'tools'
  content: string
  agent?: 'coordinator' | 'planner' | 'researcher' | 'coder' | 'analyst' | 'reporter'
  metadata?: Record<string, any>
}

export interface UpdateMessageRequest {
  content?: string
  status?: number
  metadata?: Record<string, any>
}

export interface GetMessagesRequest extends PaginationParams {
  threadId: string
  type?: 'user' | 'assistant' | 'system' | 'interrupt' | 'tools'
  status?: number
}

// ===================== Task 相关类型 =====================

export interface Task {
  id: string
  threadId: string
  title: string
  description?: string
  type: 'frontend' | 'backend' | 'integration'
  status: 'pending' | 'in_progress' | 'completed' | 'deleted'
  priority: number
  progress: number
  dependencies?: string[]
  completion_notes?: string
  deployment_url?: string
  created_at: Date | string
  updated_at: Date | string
}

export interface CreateTaskRequest {
  threadId: string
  title: string
  description?: string
  type?: 'frontend' | 'backend' | 'integration'
  priority?: number
  dependencies?: string[]
}

export interface UpdateTaskStatusRequest {
  status: 'pending' | 'in_progress' | 'completed' | 'deleted'
  progress?: number
}

export interface CompleteTaskRequest {
  completion_notes?: string
  deployment_url?: string
}

export interface GetTasksRequest extends PaginationParams {
  threadId: string
  type?: 'frontend' | 'backend' | 'integration'
  status?: 'pending' | 'in_progress' | 'completed' | 'deleted'
  priority?: number
}

export interface TaskProgress {
  total: number
  pending: number
  in_progress: number
  completed: number
  deleted: number
}

export interface GenerateTasksRequest {
  threadId: string
  prd_content?: string
  requirements?: string[]
}

// ===================== Chat 相关类型 =====================

export interface ChatStreamRequest {
  threadId: string
  messages: { role: string; content: string }[]
  interruptFeedback?: string // 用于恢复 agent 的中断反馈
}

export interface ChatRegenerateRequest {
  threadId: string
  message_id: string
}
