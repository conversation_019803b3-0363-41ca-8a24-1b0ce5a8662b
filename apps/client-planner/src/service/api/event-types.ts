/**
 * 后端实际发送的事件类型枚举
 * 基于 chat.controller.ts 分析的实际发送事件
 */
export enum ChatEventType {
  MESSAGE_CHUNK = 'message_chunk',
  INTERRUPT = 'interrupt',
  ERROR = 'error',
  TASK_COMPLETE = 'task_complete',
  END = 'end'
}

/**
 * 通用事件接口
 */
export interface GenericEvent<T extends ChatEventType, D = any> {
  type: T
  data: D & {
    timestamp?: string
    threadId?: string
  }
}

/**
 * 消息块事件 - 流式传输的文本内容
 */
export interface MessageChunkEvent extends GenericEvent<ChatEventType.MESSAGE_CHUNK, {
  content?: string
  id?: string
  agent?: string
  role?: string
  finish_reason?: string
}> {}

/**
 * 中断事件 - 需要用户反馈时触发
 */
export interface InterruptEvent extends GenericEvent<ChatEventType.INTERRUPT, {
  id?: string
  agent?: string
  content?: string
  role?: string
  finish_reason?: string
  message?: string
  options?: Array<{ text: string; value: string }>
  context?: Record<string, any>
}> {}

/**
 * 错误事件 - 处理过程中发生错误
 */
export interface ErrorEvent extends GenericEvent<ChatEventType.ERROR, {
  error: string
  code?: string
  details?: any
  recoverable?: boolean
}> {}

/**
 * 任务完成事件 - 任务规划完成
 */
export interface TaskCompleteEvent extends GenericEvent<ChatEventType.TASK_COMPLETE, {
  message: string
  tasks?: Array<{
    id: string
    title: string
    type: string
    priority: number
  }>
  timestamp: string
}> {}

/**
 * 流结束事件 - 整个流程结束
 */
export interface EndEvent extends GenericEvent<ChatEventType.END, {
  reason: 'completed' | 'cancelled' | 'error' | 'timeout'
  summary?: string
  results?: {
    totalTasks?: number
    frontendTasks?: number
    backendTasks?: number
    tasks?: any[]
  }
  nextActions?: string[]
  timestamp?: string
}> {}

/**
 * 所有聊天事件的联合类型
 */
export type ChatEvent =
  | MessageChunkEvent
  | InterruptEvent
  | ErrorEvent
  | TaskCompleteEvent
  | EndEvent