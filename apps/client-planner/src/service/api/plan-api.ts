// apps/client-planner/src/service/api/plan-api.ts
// 与后端语义化API对接的客户端


import type {
  ApiResponse,
  PaginatedResponse,
  Thread,
  CreateThreadRequest,
  UpdateThreadRequest,
  GetThreadsRequest,
  StartPlanningRequest,
  SubmitPRDRequest,
  Message,
  CreateMessageRequest,
  UpdateMessageRequest,
  GetMessagesRequest,
  Task,
  CreateTaskRequest,
  UpdateTaskStatusRequest,
  CompleteTaskRequest,
  GetTasksRequest,
  TaskProgress,
  GenerateTasksRequest,
  ChatStreamRequest,
  ChatRegenerateRequest,
  InterruptInfo,
} from './plan-types';

// 基础请求配置
const API_BASE_URL = 'http://localhost:4000/airstar-ai-api';

// 通用请求方法
async function request<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}

// GET请求方法
async function get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
  const url = params ? `${endpoint}?${new URLSearchParams(params).toString()}` : endpoint;
  return request<T>(url, { method: 'GET' });
}

// POST请求方法
async function post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
  return request<T>(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
}

// ===================== Thread API =====================

export const threadApi = {
  // 获取线程列表
  async getThreads(params?: GetThreadsRequest): Promise<PaginatedResponse<Thread>> {
    const response = await get<PaginatedResponse<Thread>>('/plan/getThreads', params);
    return response.data!;
  },

  // 创建线程
  async createThread(data: CreateThreadRequest): Promise<Thread> {
    const response = await post<Thread>('/plan/createThread', data);
    return response.data!;
  },

  // 获取单个线程
  async getThread(threadId: string): Promise<Thread> {
    const response = await get<Thread>(`/plan/getThread`, { threadId: threadId });
    return response.data!;
  },

  // 更新线程
  async updateThread(threadId: string, data: UpdateThreadRequest): Promise<Thread> {
    const response = await post<Thread>('/plan/updateThread', {
      threadId: threadId,
      ...data
    });
    return response.data!;
  },

  // 删除线程
  async deleteThread(threadId: string): Promise<{ message: string }> {
    const response = await post<{ message: string }>('/plan/deleteThread', {
      threadId: threadId
    });
    return response.data!;
  },

  // 新增：获取中断状态
  async getInterruptStatus(threadId: string): Promise<InterruptInfo> {
    const response = await get<InterruptInfo>(`/plan/thread/${threadId}/interrupt-status`);
    return response.data!;
  },

  // 开始规划
  async startPlanning(data: StartPlanningRequest): Promise<{ message: string }> {
    const response = await post<{ message: string }>('/plan/startPlanning', data);
    return response.data!;
  },

  // 提交PRD
  async submitPRD(data: SubmitPRDRequest): Promise<{ message: string }> {
    const response = await post<{ message: string }>('/plan/submitPRD', data);
    return response.data!;
  },

  // 归档线程
  async archiveThread(threadId: string): Promise<{ message: string }> {
    const response = await post<{ message: string }>('/plan/archiveThread', {
      threadId: threadId
    });
    return response.data!;
  },

  // 导出会话
  async exportSession(threadId: string): Promise<{ export_data: any }> {
    const response = await post<{ export_data: any }>('/plan/exportSession', {
      threadId: threadId
    });
    return response.data!;
  }
};

// ===================== Message API =====================

export const messageApi = {
  // 获取消息列表
  async getMessages(params: GetMessagesRequest): Promise<PaginatedResponse<Message>> {
    const response = await get<PaginatedResponse<Message>>('/plan/getMessages', params);
    return response.data!;
  },

  // 添加消息
  async addMessage(data: CreateMessageRequest): Promise<Message> {
    const response = await post<Message>('/plan/addMessage', data);
    return response.data!;
  },

  // 更新消息
  async updateMessage(messageId: string, data: UpdateMessageRequest): Promise<Message> {
    const response = await post<Message>('/plan/updateMessage', {
      message_id: messageId,
      ...data
    });
    return response.data!;
  },

  // 删除消息
  async deleteMessage(messageId: string): Promise<{ message: string }> {
    const response = await post<{ message: string }>('/plan/message/deleteMessage', {
      message_id: messageId
    });
    return response.data!;
  }
};

// ===================== Task API =====================

export const taskApi = {
  // 获取任务列表
  async getTasks(params: GetTasksRequest): Promise<PaginatedResponse<Task>> {
    const response = await get<PaginatedResponse<Task>>('/plan/getTasks', params);
    return response.data!;
  },

  // 创建任务
  async createTask(data: CreateTaskRequest): Promise<Task> {
    const response = await post<Task>('/plan/createTask', data);
    return response.data!;
  },

  // 更新任务状态
  async updateTaskStatus(taskId: string, data: UpdateTaskStatusRequest): Promise<Task> {
    const response = await post<Task>('/plan/updateTaskStatus', {
      task_id: taskId,
      ...data
    });
    return response.data!;
  },

  // 完成任务
  async completeTask(taskId: string, data?: CompleteTaskRequest): Promise<Task> {
    const response = await post<Task>('/plan/completeTask', {
      task_id: taskId,
      ...data
    });
    return response.data!;
  },

  // 删除任务
  async deleteTask(taskId: string): Promise<{ message: string }> {
    const response = await post<{ message: string }>('/plan/deleteTaskNew', {
      task_id: taskId
    });
    return response.data!;
  },

  // 获取任务进度
  async getTaskProgress(threadId: string): Promise<TaskProgress> {
    const response = await get<TaskProgress>('/plan/getTaskProgress', {
      threadId: threadId
    });
    return response.data!;
  },

  // 生成任务
  async generateTasks(data: GenerateTasksRequest): Promise<Task[]> {
    const response = await post<Task[]>('/plan/generateTasks', data);
    return response.data!;
  }
};

// ===================== Chat API =====================

export const chatApi = {
  // 流式聊天
  async streamChat(data: ChatStreamRequest): Promise<ReadableStream> {
    const response = await fetch(`${API_BASE_URL}/plan/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.body!;
  },

  // 重新生成聊天
  async regenerateChat(data: ChatRegenerateRequest): Promise<ReadableStream> {
    const response = await fetch(`${API_BASE_URL}/plan/stream/regenerate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.body!;
  }
};
