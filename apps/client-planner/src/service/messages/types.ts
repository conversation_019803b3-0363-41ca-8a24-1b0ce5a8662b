export type MessageRole = 'user' | 'assistant' | 'tool'

export interface Message {
  id: string
  threadId: string
  agent?: 'coordinator' | 'planner' | 'researcher' | 'coder' | 'analyst' | 'reporter' | 'podcast'
  role: MessageRole
  isStreaming?: boolean
  content: string
  contentChunks: string[]
  toolCalls?: ToolCallRuntime[]
  options?: Option[]
  interruptFeedback?: string
  timestamp?: string
  metadata?: MessageMetadata
}

// 消息元数据接口
export interface MessageMetadata {
  taskStatus?: {
    status: 'started' | 'in_progress' | 'completed' | 'failed' | 'paused'
    taskId?: string
    taskName?: string
    progress?: number
    message?: string
    metadata?: Record<string, any>
  }
  systemStatus?: {
    status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'ready'
    message?: string
    error?: string
  }
  error?: {
    error: string
    code?: string
    details?: Record<string, any>
    recoverable?: boolean
  }
  endInfo?: {
    reason: 'completed' | 'cancelled' | 'error' | 'timeout'
    summary?: string
    results?: Record<string, any>
    nextActions?: string[]
  }
  progress?: {
    step: string
    current: number
    total: number
    percentage: number
    estimatedTimeRemaining?: number
    message?: string
  }
  resource?: {
    action: 'created' | 'updated' | 'deleted' | 'uploaded' | 'downloaded'
    resourceType: 'file' | 'image' | 'document' | 'code' | 'data'
    resourceId: string
    resourceName?: string
    resourceUrl?: string
    metadata?: Record<string, any>
  }
  notification?: {
    level: 'info' | 'warning' | 'error' | 'success'
    title: string
    message: string
    actionable?: boolean
    actions?: Array<{
      label: string
      action: string
      data?: Record<string, any>
    }>
  }
  // AMIS表单相关配置
  amisForm?: any
  formConfig?: any
  // 后端消息ID追踪字段
  backendMessageId?: string
  // 支持更通用的元数据字段
  [key: string]: any
}

export interface Option {
  text: string
  value: string
}

export interface ToolCallRuntime {
  id: string
  name: string
  args: Record<string, unknown>
  argsChunks?: string[]
  result?: string
}
