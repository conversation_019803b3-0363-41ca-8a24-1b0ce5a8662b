import { deepClone } from '@/utils/deep-clone'
import {
  ChatEventType,
  type Chat<PERSON><PERSON>,
  type MessageChunkEvent,
  type InterruptEvent,
  type ErrorEvent,
  type TaskCompleteEvent,
  type EndEvent
} from '../api/event-types'
import type { Message } from './types'

export function mergeMessage(message: Message, event: ChatEvent) {
  console.log('data', { message, event })

  // 更新基础字段
  if (event?.data?.timestamp) {
    message.timestamp = event.data.timestamp
  }

  // 根据事件类型处理
  switch (event.type) {
    case ChatEventType.MESSAGE_CHUNK:
      mergeTextMessage(message, event as MessageChunkEvent)
      break
    case ChatEventType.INTERRUPT:
      mergeInterruptMessage(message, event as InterruptEvent)
      break
    case ChatEventType.ERROR:
      mergeErrorMessage(message, event as ErrorEvent)
      break
    case ChatEventType.END:
      mergeEndMessage(message, event as EndEvent)
      break
    case ChatEventType.TASK_COMPLETE:
      mergeTaskCompleteMessage(message, event as TaskCompleteEvent)
      break
    default:
      console.warn('Unknown event type:', (event as any).type)
  }

  // 处理完成状态
  if ((event.data as any).finish_reason) {
    message.isStreaming = false
  }

  return deepClone(message)
}

function mergeTextMessage(message: Message, event: MessageChunkEvent) {
  if (event.data.content) {
    message.content += event.data.content
    message.contentChunks.push(event.data.content)
  }
}

function mergeInterruptMessage(message: Message, event: InterruptEvent) {
  console.log('[mergeInterruptMessage] 合并中断消息:', { event, message })
  console.log('[mergeInterruptMessage] 事件上下文:', event.data.context)

  message.isStreaming = false
  message.options = event.data.options

  // 处理中断消息内容
  if (event.data.message) {
    message.content = event.data.message
  }

  // 尝试从 content 字段解析 interrupt 数据
  let interruptData: any = null
  if (event.data.content) {
    try {
      interruptData = JSON.parse(event.data.content)
      console.log('[mergeInterruptMessage] 从 content 解析到 interrupt 数据:', interruptData)
    } catch (error) {
      console.log('[mergeInterruptMessage] content 不是 JSON 格式，跳过解析')
    }
  }

  // 处理interrupt context中的prdAnalysis和amisForm
  if (event.data.context) {
    console.log('[mergeInterruptMessage] 设置 metadata from context:', event.data.context)
    message.metadata = {
      ...message.metadata,
      ...event.data.context,
    }
    console.log('[mergeInterruptMessage] 最终 metadata from context:', message.metadata)

    // 如果context中包含prdAnalysis且是XML格式，将其作为消息内容
    if (
      event.data.context.prdAnalysis &&
      (event.data.context.prdAnalysis.includes('<status>') || event.data.context.prdAnalysis.includes('<amis_json>'))
    ) {
      message.content = event.data.context.prdAnalysis
    }

    // 如果context中包含amisForm，将其设置为metadata
    if (event.data.context.amisForm) {
      message.metadata = message.metadata || {}
      message.metadata.amisForm = event.data.context.amisForm
    }
  }

  // 如果从 content 解析到了数据，将其合并到 metadata
  if (interruptData) {
    console.log('[mergeInterruptMessage] 设置 metadata from content:', interruptData)
    message.metadata = {
      ...message.metadata,
      ...interruptData,
    }

    // 如果解析到的数据中有 question，设置为消息内容
    if (interruptData.question) {
      message.content = interruptData.question
    }

    console.log('[mergeInterruptMessage] 最终 metadata:', message.metadata)
  }
}

function mergeErrorMessage(message: Message, event: ErrorEvent) {
  message.metadata = {
    ...message.metadata,
    error: {
      error: event.data.error,
      code: event.data.code,
      details: event.data.details,
      recoverable: event.data.recoverable,
    },
  }

  message.content = event.data.error
  message.isStreaming = false
}

function mergeEndMessage(message: Message, event: EndEvent) {
  message.metadata = {
    ...message.metadata,
    endInfo: {
      reason: event?.data?.reason,
      summary: event?.data?.summary,
      results: event?.data?.results,
      nextActions: event?.data?.nextActions,
    },
  }

  // 只有在消息内容为空时才设置summary，避免覆盖已有内容
  if (event.data.summary && !message.content.trim()) {
    message.content = event.data.summary
  }

  message.isStreaming = false
}

function mergeTaskCompleteMessage(message: Message, event: TaskCompleteEvent) {
  message.metadata = {
    ...message.metadata,
    taskComplete: {
      message: event?.data?.message,
      tasks: event?.data?.tasks,
      timestamp: event?.data?.timestamp,
    },
  }

  // 设置消息内容
  message.content = event.data.message
  message.isStreaming = false
}
