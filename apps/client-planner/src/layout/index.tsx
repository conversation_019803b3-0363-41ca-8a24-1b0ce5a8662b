// 导入拆分的组件
import { ChatTab } from '@/components/Layout/ChatTab'
import { TasksTab } from '@/components/Layout/TasksTab'
import { ContextTab } from '@/components/Layout/ContextTab'
import ThreadList from '@/components/ThreadList/ThreadList'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { usePlanStore } from '@/store/planStore'
import { getTaskStats, getThreadStatus } from '@/utils/threadStatus'
import { CheckCircle, Clock, Download, FileText, MessageSquare, Plus, Sparkles, Target, Database } from 'lucide-react'
import { useState } from 'react'

export default function SessionLayout() {
  const { toast } = useToast()
  const { activeThread, threads, threadsLoading, createThread, activeTab, setActiveTab } = usePlanStore()

  // 新会话标题输入
  const [newSessionTitle, setNewSessionTitle] = useState('')

  // 创建新会话
  const handleCreateNewSession = async () => {
    if (!newSessionTitle.trim()) {
      toast({
        title: '请输入会话标题',
        description: '会话标题不能为空',
        variant: 'destructive',
      })
      return
    }

    try {
      const newThread = await createThread({ title: newSessionTitle.trim() })
      if (newThread) {
        setNewSessionTitle('')
      }
    } catch (error) {
      console.error('Error creating thread in UI:', error)
    }
  }

  return (
    <div className='min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50'>
      {/* Header */}
      <div className='border-b border-white/20 bg-white/30 backdrop-blur-md'>
        <div className='container mx-auto px-6 py-2'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <div className='w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center'>
                <Sparkles className='h-6 w-6 text-white' />
              </div>
              <div>
                <h1 className='text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent'>
                  Dr.Planner
                </h1>
                <p className='text-sm text-gray-600'>AI驱动的产品需求文档处理平台</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Content */}
      <div className='container mx-auto p-6 max-w-9xl flex flex-col flex-grow overflow-hidden'>
        <div className='grid grid-cols-1 lg:grid-cols-4 gap-6 flex-grow min-h-0'>
          {/* 左侧：会话列表 */}
          <div className='lg:col-span-1 flex flex-col'>
            <div className='linear-card p-6 animate-fade-in flex-grow'>
              <div className='flex items-center gap-2 mb-6'>
                <Target className='h-5 w-5 text-purple-500' />
                <h2 className='text-lg font-semibold'>会话管理</h2>
              </div>

              <div className='space-y-4 mb-6'>
                <Input
                  placeholder='输入会话标题'
                  value={newSessionTitle}
                  onChange={(e) => setNewSessionTitle(e.target.value)}
                  className='linear-input'
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleCreateNewSession()
                    }
                  }}
                />
                <Button
                  onClick={handleCreateNewSession}
                  disabled={!newSessionTitle.trim()}
                  className='linear-button w-full'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  创建新会话
                </Button>
              </div>

              {/* 使用 ThreadList 组件 */}
              <ThreadList />
            </div>
          </div>

          {/* 右侧：主要内容区域 */}
          <div className='lg:col-span-3 flex flex-col min-h-0'>
            {activeThread ? (
              <div className='linear-card animate-fade-in flex flex-col h-full'>
                <div className='p-6 border-b border-gray-100'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <h2 className='text-xl font-semibold text-gray-900'>{activeThread.title}</h2>
                      <div className='flex items-center gap-2 mt-1'>
                        {(() => {
                          const status = getThreadStatus(activeThread)
                          const StatusIcon = status.icon
                          const taskStats = getTaskStats(activeThread.tasks)

                          return (
                            <>
                              <div className={`status-badge ${status.bgColor} ${status.color} border-current`}>
                                <StatusIcon className='h-4 w-4' />
                                <span>{status.label}</span>
                              </div>
                              {taskStats && (
                                <div className='flex items-center gap-1 text-xs text-gray-600'>
                                  <CheckCircle className='h-3 w-3 text-green-500' />
                                  <span>
                                    {taskStats.completed}/{taskStats.total}
                                  </span>
                                  {taskStats.inProgress > 0 && (
                                    <>
                                      <Clock className='h-3 w-3 text-blue-500 ml-1' />
                                      <span>{taskStats.inProgress} 进行中</span>
                                    </>
                                  )}
                                </div>
                              )}
                              <span className='text-xs text-gray-500'>
                                更新于 {new Date(activeThread.updated_at).toLocaleString()}
                              </span>
                            </>
                          )
                        })()}
                      </div>
                    </div>
                  </div>
                </div>

                <Tabs
                  value={activeTab}
                  onValueChange={(value) => setActiveTab(value as 'chat' | 'tasks' | 'context')}
                  className='w-full flex-1 flex flex-col min-h-0'
                >
                  <div className='px-6 pt-4 flex-shrink-0'>
                    <TabsList className='grid w-full grid-cols-3 bg-gray-100/50 p-1 rounded-xl'>
                      <TabsTrigger
                        value='chat'
                        className='rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm'
                      >
                        <MessageSquare className='h-4 w-4 mr-2' />
                        AI 对话
                      </TabsTrigger>
                      <TabsTrigger
                        value='tasks'
                        className='rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm'
                      >
                        <Target className='h-4 w-4 mr-2' />
                        任务列表
                      </TabsTrigger>
                      <TabsTrigger
                        value='context'
                        className='rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm'
                      >
                        <Database className='h-4 w-4 mr-2' />
                        上下文
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <TabsContent value='chat' className='flex-1 min-h-0 overflow-hidden'>
                    <ChatTab sessionId={activeThread.id.toString()} />
                  </TabsContent>

                  <TabsContent value='tasks' className='flex-1 min-h-0 overflow-hidden'>
                    <TasksTab activeSession={activeThread} />
                  </TabsContent>

                  <TabsContent value='context' className='flex-1 min-h-0 overflow-hidden'>
                    <ContextTab activeSession={activeThread} />
                  </TabsContent>
                </Tabs>
              </div>
            ) : (
              <div className='linear-card p-12 text-center animate-fade-in flex-grow'>
                <div className='flex-1 flex flex-col overflow-hidden justify-center h-full'>
                  <div className='w-20 h-20 bg-gradient-to-r from-purple-100 to-blue-100 rounded-3xl flex items-center justify-center mx-auto mb-6'>
                    <Sparkles className='h-10 w-10 text-purple-500' />
                  </div>
                  <h3 className='text-xl font-semibold text-gray-900 mb-3'>欢迎使用 PRD 智能处理器</h3>
                  <p className='text-gray-600 max-w-md mx-auto mb-6'>
                    选择现有会话继续工作，或创建新会话开始 AI 驱动的产品需求文档处理流程
                  </p>
                  <div className='flex items-center justify-center gap-8 text-sm text-gray-500'>
                    <div className='flex items-center gap-2'>
                      <MessageSquare className='h-4 w-4' />
                      <span>智能对话</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Target className='h-4 w-4' />
                      <span>任务生成</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <FileText className='h-4 w-4' />
                      <span>结构化输出</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
