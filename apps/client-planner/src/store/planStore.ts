// apps/client-planner/src/store/planStore.ts
// 规划功能的状态管理
import { ChatEventType, type ChatEvent } from '@/service/api/event-types'
import { mergeMessage } from '@/service/messages/merge-message'
import type { Message as HookMessage, MessageRole as HookMessageRole } from '@/service/messages/types'
import { nanoid } from 'nanoid'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

import { chatApi, messageApi, taskApi, threadApi } from '../service/api/plan-api'
import type {
  CreateTaskRequest,
  CreateThreadRequest,
  Message as PlanApiMessage, // Renamed to avoid conflict
  Task,
  TaskProgress,
  Thread,
  UpdateTaskStatusRequest,
  UpdateThreadRequest,
} from '../service/api/plan-types'

// Renamed to avoid conflict

// 基础请求配置
const API_BASE_URL = 'http://localhost:4000/airstar-ai-api'

// SSE数据类型定义
// ===================== 状态类型定义 =====================

interface PlanState {
  // 线程相关状态
  threads: Thread[]
  activeThreadId: string | null
  activeThread: Thread | null // Derived state
  threadsLoading: boolean
  threadsError: string | null

  // 消息相关状态
  messages: HookMessage[]
  messagesLoading: boolean
  messagesError: string | null

  // 任务相关状态
  tasks: Task[]
  tasksLoading: boolean
  tasksError: string | null
  taskProgress: TaskProgress | null

  // 聊天相关状态
  isLoading: boolean // Renamed from chatStreaming
  chatError: string | null
  waitingForFeedback: boolean
  interruptMessage: HookMessage | null

  // UI状态 - 新增Tab管理
  activeTab: 'chat' | 'tasks' | 'context'
  setActiveTab: (tab: 'chat' | 'tasks' | 'context') => void

  // 线程操作
  fetchThreads: (params?: { user_id?: string; status?: number; page?: number; limit?: number }) => Promise<void>
  createThread: (data: CreateThreadRequest) => Promise<Thread | null>
  updateThread: (threadId: string, data: UpdateThreadRequest) => Promise<void>
  deleteThread: (threadId: string) => Promise<void>
  setActiveThread: (threadId: string | null) => void // Restored name, but logic takes threadId
  archiveThread: (threadId: string) => Promise<void>

  // 消息操作
  loadSessionMessages: (threadId: string) => Promise<void>
  // sendMessage will handle adding messages now

  // 任务操作
  fetchTasks: (
    threadId: string,
    params?: {
      status?: 'pending' | 'in_progress' | 'completed' | 'deleted'
      type?: 'frontend' | 'backend' | 'integration'
      page?: number
      limit?: number
    }
  ) => Promise<void>
  createTask: (data: CreateTaskRequest) => Promise<Task | null>
  updateTaskStatus: (taskId: string, data: UpdateTaskStatusRequest) => Promise<void>
  completeTask: (taskId: string, data?: { completion_notes?: string; deployment_url?: string }) => Promise<void>
  deleteTask: (taskId: string) => Promise<void>
  fetchTaskProgress: (threadId: string) => Promise<void>
  clearTasks: () => void

  // 聊天操作
  sendMessage: (content: string, isFeedback?: boolean) => Promise<void>

  // 新增：中断状态检查
  checkInterruptStatus: (threadId: string) => Promise<void>

  // 清理操作
  clearAll: () => void
}

// ===================== Store 实现 =====================

export const usePlanStore = create<PlanState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      threads: [],
      activeThreadId: null,
      activeThread: null,
      threadsLoading: false,
      threadsError: null,

      messages: [],
      messagesLoading: false,
      messagesError: null,

      tasks: [],
      tasksLoading: false,
      tasksError: null,
      taskProgress: null,

      isLoading: false,
      chatError: null,
      waitingForFeedback: false,
      interruptMessage: null,

      // UI状态 - 新增Tab管理
      activeTab: 'chat',
      setActiveTab: (tab) => set({ activeTab: tab }),

      // ===================== 线程操作 =====================

      fetchThreads: async (params) => {
        set({ threadsLoading: true, threadsError: null })
        try {
          const response = await threadApi.getThreads(params)
          set((state) => ({
            threads: response.items,
            threadsLoading: false,
            activeThread: state.threads.find((t) => t.id === state.activeThreadId) || null,
          }))
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '获取线程列表失败'
          set({
            threadsError: errorMessage,
            threadsLoading: false,
          })
        }
      },

      createThread: async (data) => {
        set({ threadsLoading: true, threadsError: null })
        try {
          const newThread = await threadApi.createThread(data)

          // 创建成功后重新获取线程列表，确保与后端状态同步
          await get().fetchThreads()

          set((state) => ({
            activeThreadId: newThread.id,
            activeThread: newThread,
            threadsLoading: false,
          }))
          get().loadSessionMessages(newThread.id)
          return newThread
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '创建线程失败'
          set({
            threadsError: errorMessage,
            threadsLoading: false,
          })
          return null
        }
      },

      updateThread: async (threadId, data) => {
        try {
          const updatedThread = await threadApi.updateThread(threadId, data)
          set((state) => {
            const threads = state.threads.map((t) => (t.id === threadId ? { ...t, ...updatedThread } : t))
            return {
              threads,
              activeThread:
                state.activeThreadId === threadId ? threads.find((t) => t.id === threadId) || null : state.activeThread,
            }
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '更新线程失败'
          set({ threadsError: errorMessage })
        }
      },

      deleteThread: async (threadId) => {
        try {
          await threadApi.deleteThread(threadId)
          // 删除成功后重新获取线程列表，确保与后端状态同步
          await get().fetchThreads()

          set((state) => ({
            threads: state.threads.filter((t) => t.id !== threadId),
            activeThreadId: state.activeThreadId === threadId ? null : state.activeThreadId,
            activeThread: state.activeThreadId === threadId ? null : state.activeThread,
          }))
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '删除线程失败'
          set({ threadsError: errorMessage })
        }
      },

      setActiveThread: (threadId) => {
        // 获取目标线程信息，用于智能Tab切换
        const targetThread = threadId ? get().threads.find((t) => t.id === threadId) : null

        set((state) => ({
          activeThreadId: threadId,
          activeThread: state.threads.find((t) => t.id === threadId) || null,
          messages: [],
          tasks: [],
          taskProgress: null,
          messagesError: null,
          tasksError: null,
          // 重置中断状态，稍后会检查
          waitingForFeedback: false,
          interruptMessage: null,
        }))

        if (threadId) {
          get().loadSessionMessages(threadId)
          get().fetchTasks(threadId)
          get().fetchTaskProgress(threadId)
          // 新增：检查中断状态
          get().checkInterruptStatus(threadId)

          // 智能切换Tab：如果该会话有任务，自动切换到任务Tab
          if (targetThread && Array.isArray(targetThread.tasks) && targetThread.tasks.length > 0) {
            get().setActiveTab('tasks')
          } else {
            get().setActiveTab('chat')
          }
        }
      },

      archiveThread: async (threadId) => {
        try {
          await threadApi.archiveThread(threadId)
          // 更新本地状态，将线程标记为已归档
          set((state) => ({
            threads: state.threads.map((t) => (t.id === threadId ? { ...t, status: 0 } : t)),
          }))
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '归档线程失败'
          set({ threadsError: errorMessage })
        }
      },

      // ===================== 消息操作 =====================
      loadSessionMessages: async (threadId: string) => {
        set({ messagesLoading: true, messagesError: null })
        try {
          const response = await messageApi.getMessages({ threadId })
          const apiMessages = response.items

          const hookMessages: HookMessage[] = apiMessages.map((apiMsg: PlanApiMessage) => {
            return {
              id: apiMsg.id,
              threadId: threadId,
              role: apiMsg.type === 'system' ? 'assistant' : (apiMsg.type as HookMessageRole),
              content: apiMsg.content || '',
              contentChunks: [apiMsg.content || ''],
              isStreaming: false,
              timestamp: apiMsg.created_at ? new Date(apiMsg.created_at).toISOString() : new Date().toISOString(),
              agent: apiMsg.agent as any,
              toolCalls: undefined,
              options: undefined,
              interruptFeedback: undefined,
            }
          })

          set({ messages: hookMessages, messagesLoading: false })
        } catch (error) {
          console.error('[planStore] 加载会话消息失败:', error)
          const errorMessage = error instanceof Error ? error.message : '获取消息列表失败'
          set({ messagesError: errorMessage, messagesLoading: false })
        }
      },

      // ===================== 任务操作 =====================

      fetchTasks: async (threadId, params) => {
        set({ tasksLoading: true, tasksError: null })
        try {
          const response = await taskApi.getTasks({ threadId: threadId, ...params })
          set({
            tasks: response.items.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()),
            tasksLoading: false,
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '获取任务列表失败'
          set({
            tasksError: errorMessage,
            tasksLoading: false,
          })
        }
      },

      createTask: async (data) => {
        try {
          const newTask = await taskApi.createTask(data)
          set((state) => ({
            tasks: [...state.tasks, newTask],
          }))
          // 更新任务进度
          if (data.threadId) {
            get().fetchTaskProgress(data.threadId)
          }
          return newTask
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '创建任务失败'
          set({ tasksError: errorMessage })
          return null
        }
      },

      updateTaskStatus: async (taskId, data) => {
        try {
          const updatedTask = await taskApi.updateTaskStatus(taskId, data)
          set((state) => ({
            tasks: state.tasks.map((t) => (t.id === taskId ? updatedTask : t)),
          }))
          // 更新任务进度
          if (updatedTask.threadId) {
            get().fetchTaskProgress(updatedTask.threadId)
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '更新任务状态失败'
          set({ tasksError: errorMessage })
        }
      },

      completeTask: async (taskId, data) => {
        try {
          const completedTask = await taskApi.completeTask(taskId, data)
          set((state) => ({
            tasks: state.tasks.map((t) => (t.id === taskId ? completedTask : t)),
          }))
          // 更新任务进度
          if (completedTask.threadId) {
            get().fetchTaskProgress(completedTask.threadId)
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '完成任务失败'
          set({ tasksError: errorMessage })
        }
      },

      deleteTask: async (taskId) => {
        try {
          await taskApi.deleteTask(taskId)
          const task = get().tasks.find((t) => t.id === taskId)
          set((state) => ({
            tasks: state.tasks.filter((t) => t.id !== taskId),
          }))
          // 更新任务进度
          if (task?.threadId) {
            get().fetchTaskProgress(task.threadId)
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '删除任务失败'
          set({ tasksError: errorMessage })
        }
      },

      fetchTaskProgress: async (threadId) => {
        try {
          const progress = await taskApi.getTaskProgress(threadId)
          set({ taskProgress: progress })
        } catch (error) {
          console.warn('获取任务进度失败:', error)
        }
      },

      clearTasks: () => {
        set({ tasks: [], tasksError: null, taskProgress: null })
      },

      // ===================== 聊天操作 =====================

      sendMessage: async (content: string, isFeedback: boolean = false) => {
        if (!content.trim()) return
        const threadId = get().activeThreadId
        if (!threadId) {
          console.error('No active thread to send message to.')
          return
        }

        set({ isLoading: true, waitingForFeedback: false, interruptMessage: null, chatError: null })

        // --- Helper functions from use-planner-chat ---
        // 使用两个Map：一个跟踪后端ID到前端消息的映射，一个跟踪流式消息
        const backendIdToFrontendId = new Map<string, string>() // 后端ID -> 前端消息ID
        const streamingMessages = new Map<string, HookMessage>() // 前端消息ID -> 消息对象

        const createMessage = (
          content: string,
          role: HookMessageRole = 'assistant',
          agent?: string,
          frontendId?: string
        ): HookMessage => {
          return {
            id: frontendId || nanoid(),
            threadId: get().activeThreadId!,
            role,
            agent: agent as any,
            content,
            contentChunks: [],
            isStreaming: role === 'assistant',
            timestamp: new Date().toISOString(),
          }
        }

        const addMessage = (message: HookMessage) => {
          set((state) => ({ messages: [...state.messages, message] }))
        }

        const updateMessage = (messageId: string, updates: Partial<HookMessage>) => {
          set((state) => ({
            messages: state.messages.map((msg) => (msg.id === messageId ? { ...msg, ...updates } : msg)),
          }))
        }

        const handleChatEvent = (event: ChatEvent) => {
          // 从事件数据中提取后端消息 ID，不同事件类型可能有不同的ID字段
          let backendMessageId: string
          if (event.type === 'task_complete' || event.type === 'end') {
            // task_complete和end事件使用threadId作为唯一标识
            backendMessageId = event.data?.threadId || 'default'
          } else {
            // 其他事件类型使用id字段
            backendMessageId = (event.data as any)?.id || 'default'
          }

          // 查找是否已有对应的前端消息
          let frontendMessageId = backendIdToFrontendId.get(backendMessageId)
          let currentMessage = frontendMessageId ? streamingMessages.get(frontendMessageId) : null

          // 对于task_complete和end事件，总是创建新消息
          if (!currentMessage || event.type === ChatEventType.TASK_COMPLETE || event.type === ChatEventType.END) {
            // 创建新的消息，使用前端生成的ID
            const newFrontendId = nanoid()
            const isStreamingEvent = ![ChatEventType.TASK_COMPLETE, ChatEventType.END, ChatEventType.ERROR].includes(event.type)
            const newMessage = createMessage('', 'assistant', (event.data as any)?.agent || 'planner', newFrontendId)

            // 对于非流式事件，立即标记为非流式状态
            if (!isStreamingEvent) {
              newMessage.isStreaming = false
            }

            currentMessage = newMessage
            backendIdToFrontendId.set(backendMessageId, newFrontendId)
            if (isStreamingEvent) {
              streamingMessages.set(newFrontendId, currentMessage)
            }
            addMessage(newMessage)
          }

          const updatedMessage = mergeMessage(currentMessage, event)
          updateMessage(currentMessage.id, updatedMessage)

          switch (event.type) {
            case ChatEventType.INTERRUPT:
              console.log('waitingForFeedback set true 2')
              set({
                waitingForFeedback: true,
                interruptMessage: updatedMessage,
              })
              // 标记该消息流式结束，清理映射
              backendIdToFrontendId.delete(backendMessageId)
              streamingMessages.delete(currentMessage.id)
              break

            case ChatEventType.TASK_COMPLETE:
              // 任务完成后刷新任务列表
              if (threadId) {
                get().fetchTasks(threadId)
              }
              // 自动切换到任务Tab
              get().setActiveTab('tasks')
              // 清理该消息的映射（task_complete事件不在streamingMessages中）
              backendIdToFrontendId.delete(backendMessageId)
              // 检查是否还有其他流式消息在进行
              if (streamingMessages.size === 0) {
                set({ isLoading: false })
              }
              break

            case ChatEventType.END:
              // 如果是工作流完成，也切换到任务Tab
              if (event.data?.reason === 'completed' && event.data?.results?.totalTasks && event.data.results.totalTasks > 0) {
                get().setActiveTab('tasks')
              }
              // 清理该消息的映射（end事件不在streamingMessages中）
              backendIdToFrontendId.delete(backendMessageId)
              // 检查是否还有其他流式消息在进行
              if (streamingMessages.size === 0) {
                set({ isLoading: false })
              }
              break

            case ChatEventType.ERROR:
              set({ isLoading: false, chatError: event.data?.error || '未知错误' })
              // 清除所有流式消息和映射
              backendIdToFrontendId.clear()
              streamingMessages.clear()
              break
          }

          // 更新本地引用
          if (currentMessage) {
            streamingMessages.set(currentMessage.id, { ...currentMessage, ...updatedMessage })
          }
        }
        // --- End of helper functions ---

        try {
          // Add user message to UI and DB
          const userMessage = createMessage(content, 'user')
          addMessage(userMessage)

          await messageApi.addMessage({
            threadId: threadId,
            type: 'user',
            content: content,
          })

          // Stream chat request
          const stream = await chatApi.streamChat({
            threadId: threadId,
            messages: [
              ...get().messages.map((msg) => ({
                role: msg.role,
                content: msg.content,
              })),
              { role: 'user', content },
            ],
            // 如果是反馈消息，将内容作为 interruptFeedback 传递
            ...(isFeedback && { interruptFeedback: content }),
          })

          // Process stream
          const reader = stream.getReader()
          const decoder = new TextDecoder()
          try {
            while (true) {
              const { done, value } = await reader.read()
              if (done) break

              const chunk = decoder.decode(value, { stream: true })
              const lines = chunk.split('\n')

              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  try {
                    const rawData = JSON.parse(line.slice(6))

                    // 处理双重JSON嵌套的情况
                    let eventData
                    if (rawData.data && typeof rawData.data === 'string') {
                      // 后端发送的数据格式：{"data": "{\"type\":\"...\"}"}
                      eventData = JSON.parse(rawData.data)
                    } else {
                      // 直接的事件数据格式：{"type": "...", "data": {...}}
                      eventData = rawData
                    }

                    handleChatEvent(eventData)
                  } catch (parseError) {
                    console.error('Failed to parse SSE data:', parseError, 'Raw line:', line)
                  }
                }
              }
            }
          } finally {
            reader.releaseLock()
          }
          set((prev) => ({ ...prev, isLoading: false }))
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '发送消息失败'
          set({ chatError: errorMessage, isLoading: false })
        }
      },

      // ===================== 清理操作 =====================

      clearAll: () => {
        set({
          threads: [],
          activeThreadId: null,
          activeThread: null,
          threadsLoading: false,
          threadsError: null,
          messages: [],
          messagesLoading: false,
          messagesError: null,
          tasks: [],
          tasksLoading: false,
          tasksError: null,
          taskProgress: null,
          isLoading: false,
          chatError: null,
          waitingForFeedback: false,
          interruptMessage: null,
          activeTab: 'chat',
        })
      },

      // 新增：检查中断状态
      checkInterruptStatus: async (threadId: string) => {
        try {
          const interruptInfo = await threadApi.getInterruptStatus(threadId)

          if (interruptInfo.isWaitingFeedback) {
            // 创建一个中断消息来显示等待反馈的状态
            const interruptMessage: HookMessage = {
              id: `interrupt-${threadId}`,
              threadId: threadId,
              role: 'assistant',
              content: 'AI 正在等待您的反馈...',
              contentChunks: ['AI 正在等待您的反馈...'],
              isStreaming: false,
              timestamp: interruptInfo.interruptAt
                ? new Date(interruptInfo.interruptAt).toISOString()
                : new Date().toISOString(),
            }
            console.log('waitingForFeedback set true')
            set({
              waitingForFeedback: true,
              interruptMessage: interruptMessage,
            })
          }
        } catch (error) {
          console.warn('检查中断状态失败:', error)
          // 不要抛出错误，只是记录警告
        }
      },
    }),
    {
      name: 'plan-store',
    }
  )
)
