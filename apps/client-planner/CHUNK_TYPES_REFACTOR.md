# Chunk 类型重构总结

## 概述
本次重构统一了前后端的事件类型管理，删除了后端不发送的事件类型，使用枚举值管理所有chunk类型。

## 后端实际发送的事件类型
通过分析 `apps/server-ai/src/plan/chat.controller.ts`，确认后端实际只发送以下5种事件类型：

1. **message_chunk** - 流式传输的文本内容块
2. **interrupt** - 中断事件，需要用户反馈
3. **error** - 错误事件
4. **task_complete** - 任务规划完成事件
5. **end** - 工作流结束事件

## 删除的事件类型
以下事件类型在前端定义但后端不发送，已全部删除：

- `tool_calls` - 工具调用事件
- `tool_call_chunks` - 工具调用块事件
- `tool_call_result` - 工具调用结果事件
- `task_status` - 任务状态事件
- `system_status` - 系统状态事件
- `progress` - 进度事件
- `resource` - 资源事件
- `notification` - 通知事件

## 主要修改

### 1. 创建统一的事件类型枚举
**前端文件**: `apps/client-planner/src/service/api/event-types.ts`
- 新建 `ChatEventType` 枚举，包含5种后端实际发送的事件类型
- 定义对应的 TypeScript 接口
- 创建统一的 `ChatEvent` 联合类型

**后端文件**: `apps/server-ai/src/plan/types/event-types.ts`
- 新建与前端一致的 `ChatEventType` 枚举
- 定义后端事件数据接口（MessageChunkEventData, InterruptEventData等）
- 创建 `ChatEventResponse` 接口

### 2. 更新消息合并逻辑
**文件**: `apps/client-planner/src/service/messages/merge-message.ts`
- 更新导入，使用新的事件类型
- 删除所有不需要的合并函数：
  - `mergeToolCallMessage`
  - `mergeToolCallResultMessage`
  - `mergeTaskStatusMessage`
  - `mergeSystemStatusMessage`
  - `mergeProgressMessage`
  - `mergeResourceMessage`
  - `mergeNotificationMessage`
- 使用枚举值替代字符串常量

### 3. 更新状态管理
**文件**: `apps/client-planner/src/store/planStore.ts`
- 导入新的事件类型枚举
- 使用枚举值替代字符串常量进行事件类型比较
- 修复可能的空值访问问题

### 4. 精简消息卡片组件
**文件**: `apps/client-planner/src/components/chat/message-cards.tsx`
- 删除不需要的卡片组件：
  - `TaskStatusCard`
  - `ProgressCard`
  - `NotificationCard`
  - `ResourceCard`
- 保留实际使用的卡片组件：
  - `ErrorCard`
  - `InterruptCard`
  - `TaskCompleteCard`
  - `EndCard`
  - `PlannerFeedbackCard`

### 5. 更新消息渲染器
**文件**: `apps/client-planner/src/components/chat/message-renderer.tsx`
- 删除不存在卡片的导入和使用
- 简化 `MessageRendererProps` 接口
- 移除不需要的事件处理逻辑

### 6. 修复聊天界面
**文件**: `apps/client-planner/src/components/chat/planner-chat-interface.tsx`
- 删除 `TaskStatusInfo` 类型引用
- 移除不存在的组件属性传递

### 7. 后端控制器枚举化
**文件**: `apps/server-ai/src/plan/chat.controller.ts`
- 导入新的事件类型枚举
- 更新 `ProcessedChunk` 接口使用枚举类型
- 将所有硬编码字符串替换为枚举值：
  - `'message_chunk'` → `ChatEventType.MESSAGE_CHUNK`
  - `'interrupt'` → `ChatEventType.INTERRUPT` 
  - `'error'` → `ChatEventType.ERROR`
  - `'task_complete'` → `ChatEventType.TASK_COMPLETE`
  - `'end'` → `ChatEventType.END`

## 验证结果
- 所有与chunk类型相关的TypeScript错误已修复
- 代码结构更加清晰，减少了冗余代码
- 前后端事件类型完全一致，均使用枚举值管理
- 前端构建通过（仅剩依赖包缺失的无关错误）
- 后端构建完全通过

## 优势
1. **类型安全**: 使用枚举值避免了字符串拼写错误
2. **代码简洁**: 删除了大量无用代码
3. **维护性**: 前后端类型定义统一，易于维护
4. **性能优化**: 减少了不必要的事件处理逻辑

## 后续建议
1. 如需新增事件类型，应同时在后端和前端的枚举中添加
2. 确保新增的事件类型在后端 `chat.controller.ts` 中有对应的发送逻辑
3. 为新增事件类型创建对应的消息卡片组件和合并逻辑 
