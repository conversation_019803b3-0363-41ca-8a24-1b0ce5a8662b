import { FastMCP } from 'fastmcp';
import { logger } from './utils/logger.js';
import { ConfigManager } from './utils/config.js';
import { registerPlannerTools } from './tools/index.js';

/**
 * Planner MCP Server class
 */
export class PlannerMCPServer {
  private server: FastMCP;
  private config: ConfigManager;
  private initialized = false;

  constructor() {
    this.config = ConfigManager.getInstance();
    
    // Initialize FastMCP server
    this.server = new FastMCP({
      name: 'Planner MCP Server',
      version: '1.0.0',
    });

    logger.info('PlannerMCPServer instance created');
  }

  /**
   * Initialize the server
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      logger.warn('Server already initialized');
      return;
    }

    try {
      logger.info('Initializing Planner MCP Server...');

      // Register all tools
      registerPlannerTools(this.server);

      this.initialized = true;
      logger.info('Planner MCP Server initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize server', { error });
      throw error;
    }
  }

  /**
   * Start the server
   */
  public async start(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      logger.info('Starting Planner MCP Server...');

      await this.server.start({
        transportType: 'stdio',
      });

      logger.info('Planner MCP Server started successfully');

    } catch (error) {
      logger.error('Failed to start server', { error });
      throw error;
    }
  }

  /**
   * Stop the server
   */
  public async stop(): Promise<void> {
    try {
      logger.info('Stopping Planner MCP Server...');

      if (this.server) {
        await this.server.stop();
      }

      logger.info('Planner MCP Server stopped successfully');

    } catch (error) {
      logger.error('Failed to stop server', { error });
      throw error;
    }
  }

  /**
   * Get server instance (for testing)
   */
  public getServer(): FastMCP {
    return this.server;
  }

  /**
   * Check if server is initialized
   */
  public isInitialized(): boolean {
    return this.initialized;
  }
}
