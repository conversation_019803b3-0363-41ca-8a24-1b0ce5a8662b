import { FastMCP } from 'fastmcp';
import { logger } from '../utils/logger.js';

// Import all tools
import { syncTasksTool } from './sync-tasks.js';
import { getTasksTool } from './get-tasks.js';
import { updateTaskTool } from './update-task.js';
import { createTaskTool } from './create-task.js';
import { deleteTaskTool } from './delete-task.js';

/**
 * Register all planner MCP tools
 */
export function registerPlannerTools(server: FastMCP): void {
  logger.info('Registering planner MCP tools');

  // Register sync tasks tool
  server.addTool({
    name: syncTasksTool.name,
    description: syncTasksTool.description,
    parameters: syncTasksTool.inputSchema,
    execute: async (args: any, _context: any) => syncTasksTool.handler(args),
  });

  // Register get tasks tool
  server.addTool({
    name: getTasksTool.name,
    description: getTasksTool.description,
    parameters: getTasksTool.inputSchema,
    execute: async (args: any, _context: any) => getTasksTool.handler(args),
  });

  // Register update task tool
  server.addTool({
    name: updateTaskTool.name,
    description: updateTaskTool.description,
    parameters: updateTaskTool.inputSchema,
    execute: async (args: any, _context: any) => updateTaskTool.handler(args),
  });

  // Register create task tool
  server.addTool({
    name: createTaskTool.name,
    description: createTaskTool.description,
    parameters: createTaskTool.inputSchema,
    execute: async (args: any, _context: any) => createTaskTool.handler(args),
  });

  // Register delete task tool
  server.addTool({
    name: deleteTaskTool.name,
    description: deleteTaskTool.description,
    parameters: deleteTaskTool.inputSchema,
    execute: async (args: any, _context: any) => deleteTaskTool.handler(args),
  });

  logger.info('All planner MCP tools registered successfully');
}

// Export individual tools for testing
export {
  syncTasksTool,
  getTasksTool,
  updateTaskTool,
  createTaskTool,
  deleteTaskTool,
};
