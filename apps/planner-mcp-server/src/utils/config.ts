import { z } from 'zod';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Configuration schema
 */
export const ConfigSchema = z.object({
  remote: z.object({
    baseUrl: z.string(),
    apiKey: z.string().optional(),
    timeout: z.number().default(30000),
    retryAttempts: z.number().default(3),
    retryDelay: z.number().default(1000),
  }),
  local: z.object({
    storagePath: z.string().default('./plans'),
    backupEnabled: z.boolean().default(true),
    maxBackups: z.number().default(10),
  }),
  sync: z.object({
    autoSync: z.boolean().default(true),
    interval: z.number().default(300000), // 5 minutes
    conflictResolution: z.enum(['local_wins', 'remote_wins', 'manual']).default('remote_wins'),
  }),
  logging: z.object({
    level: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
    file: z.string().optional(),
  }),
});

export type Config = z.infer<typeof ConfigSchema>;

/**
 * Default configuration
 */
const defaultConfig: Config = {
  remote: {
    baseUrl: process.env.REMOTE_API_BASE_URL || 'http://localhost:3000',
    apiKey: process.env.REMOTE_API_KEY,
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
  },
  local: {
    storagePath: process.env.LOCAL_STORAGE_PATH || './plans',
    backupEnabled: true,
    maxBackups: 10,
  },
  sync: {
    autoSync: true,
    interval: parseInt(process.env.SYNC_INTERVAL || '300000'),
    conflictResolution: 'remote_wins',
  },
  logging: {
    level: (process.env.LOG_LEVEL as any) || 'info',
    file: process.env.LOG_FILE,
  },
};

/**
 * Configuration manager
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private config: Config;
  private configPath: string;

  private constructor() {
    this.configPath = path.join(process.cwd(), 'config.json');
    this.config = defaultConfig;
    this.loadConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * Load configuration from file and environment
   */
  private loadConfig(): void {
    try {
      // Load from config file if exists
      if (fs.existsSync(this.configPath)) {
        const fileConfig = fs.readJsonSync(this.configPath);
        this.config = ConfigSchema.parse({ ...defaultConfig, ...fileConfig });
      } else {
        this.config = ConfigSchema.parse(defaultConfig);
      }
    } catch (error) {
      console.warn('Failed to load config file, using defaults:', error);
      this.config = ConfigSchema.parse(defaultConfig);
    }
  }

  /**
   * Save configuration to file
   */
  public async saveConfig(): Promise<void> {
    try {
      await fs.writeJson(this.configPath, this.config, { spaces: 2 });
    } catch (error) {
      throw new Error(`Failed to save config: ${error}`);
    }
  }

  /**
   * Get configuration
   */
  public getConfig(): Config {
    return this.config;
  }

  /**
   * Update configuration
   */
  public updateConfig(updates: Partial<Config>): void {
    this.config = ConfigSchema.parse({ ...this.config, ...updates });
  }

  /**
   * Get specific config section
   */
  public getRemoteConfig() {
    return this.config.remote;
  }

  public getLocalConfig() {
    return this.config.local;
  }

  public getSyncConfig() {
    return this.config.sync;
  }

  public getLoggingConfig() {
    return this.config.logging;
  }
}
