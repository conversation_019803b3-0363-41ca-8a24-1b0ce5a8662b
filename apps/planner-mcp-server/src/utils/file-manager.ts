import fs from 'fs-extra';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { logger } from './logger.js';
import { ConfigManager } from './config.js';

/**
 * File manager for handling local storage operations
 */
export class FileManager {
  private static instance: FileManager;
  private storagePath: string;
  private backupEnabled: boolean;
  private maxBackups: number;

  private constructor() {
    const config = ConfigManager.getInstance().getLocalConfig();
    this.storagePath = path.resolve(config.storagePath);
    this.backupEnabled = config.backupEnabled;
    this.maxBackups = config.maxBackups;
    
    // Ensure storage directory exists
    fs.ensureDirSync(this.storagePath);
    logger.info(`FileManager initialized with storage path: ${this.storagePath}`);
  }

  public static getInstance(): FileManager {
    if (!FileManager.instance) {
      FileManager.instance = new FileManager();
    }
    return FileManager.instance;
  }

  /**
   * Get thread directory path
   */
  public getThreadPath(threadId: string): string {
    return path.join(this.storagePath, `thread-${threadId}`);
  }

  /**
   * Get tasks file path for a thread
   */
  public getTasksFilePath(threadId: string): string {
    return path.join(this.getThreadPath(threadId), 'tasks.json');
  }

  /**
   * Get state file path for a thread
   */
  public getStateFilePath(threadId: string): string {
    return path.join(this.getThreadPath(threadId), 'state.json');
  }

  /**
   * Get metadata file path for a thread
   */
  public getMetadataFilePath(threadId: string): string {
    return path.join(this.getThreadPath(threadId), 'metadata.json');
  }

  /**
   * Ensure thread directory exists
   */
  public async ensureThreadDirectory(threadId: string): Promise<void> {
    const threadPath = this.getThreadPath(threadId);
    await fs.ensureDir(threadPath);
    logger.debug(`Ensured thread directory: ${threadPath}`);
  }

  /**
   * Check if thread exists locally
   */
  public async threadExists(threadId: string): Promise<boolean> {
    const threadPath = this.getThreadPath(threadId);
    return fs.pathExists(threadPath);
  }

  /**
   * Read JSON file safely
   */
  public async readJsonFile<T>(filePath: string): Promise<T | null> {
    try {
      if (await fs.pathExists(filePath)) {
        const content = await fs.readJson(filePath);
        logger.debug(`Read JSON file: ${filePath}`);
        return content;
      }
      return null;
    } catch (error) {
      logger.error(`Failed to read JSON file: ${filePath}`, { error });
      return null;
    }
  }

  /**
   * Write JSON file safely with backup
   */
  public async writeJsonFile(filePath: string, data: any): Promise<void> {
    try {
      // Create backup if enabled and file exists
      if (this.backupEnabled && await fs.pathExists(filePath)) {
        await this.createBackup(filePath);
      }

      // Ensure directory exists
      await fs.ensureDir(path.dirname(filePath));

      // Write file
      await fs.writeJson(filePath, data, { spaces: 2 });
      logger.debug(`Wrote JSON file: ${filePath}`);
    } catch (error) {
      logger.error(`Failed to write JSON file: ${filePath}`, { error });
      throw error;
    }
  }

  /**
   * Create backup of a file
   */
  private async createBackup(filePath: string): Promise<void> {
    try {
      const backupDir = path.join(path.dirname(filePath), '.backups');
      await fs.ensureDir(backupDir);

      const fileName = path.basename(filePath, path.extname(filePath));
      const extension = path.extname(filePath);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `${fileName}_${timestamp}${extension}`;
      const backupPath = path.join(backupDir, backupFileName);

      await fs.copy(filePath, backupPath);
      logger.debug(`Created backup: ${backupPath}`);

      // Clean old backups
      await this.cleanOldBackups(backupDir, fileName, extension);
    } catch (error) {
      logger.warn(`Failed to create backup for: ${filePath}`, { error });
    }
  }

  /**
   * Clean old backup files
   */
  private async cleanOldBackups(backupDir: string, fileName: string, extension: string): Promise<void> {
    try {
      const files = await fs.readdir(backupDir);
      const backupFiles = files
        .filter(file => file.startsWith(fileName) && file.endsWith(extension))
        .map(file => ({
          name: file,
          path: path.join(backupDir, file),
          stat: fs.statSync(path.join(backupDir, file))
        }))
        .sort((a, b) => b.stat.mtime.getTime() - a.stat.mtime.getTime());

      // Remove excess backups
      if (backupFiles.length > this.maxBackups) {
        const filesToDelete = backupFiles.slice(this.maxBackups);
        for (const file of filesToDelete) {
          await fs.remove(file.path);
          logger.debug(`Removed old backup: ${file.path}`);
        }
      }
    } catch (error) {
      logger.warn(`Failed to clean old backups in: ${backupDir}`, { error });
    }
  }

  /**
   * List all thread IDs
   */
  public async listThreads(): Promise<string[]> {
    try {
      const items = await fs.readdir(this.storagePath);
      const threadIds = items
        .filter(item => item.startsWith('thread-'))
        .map(item => item.replace('thread-', ''))
        .filter(async (threadId) => {
          const threadPath = this.getThreadPath(threadId);
          const stat = await fs.stat(threadPath);
          return stat.isDirectory();
        });
      
      return threadIds;
    } catch (error) {
      logger.error('Failed to list threads', { error });
      return [];
    }
  }

  /**
   * Delete thread directory
   */
  public async deleteThread(threadId: string): Promise<void> {
    try {
      const threadPath = this.getThreadPath(threadId);
      if (await fs.pathExists(threadPath)) {
        await fs.remove(threadPath);
        logger.info(`Deleted thread directory: ${threadPath}`);
      }
    } catch (error) {
      logger.error(`Failed to delete thread: ${threadId}`, { error });
      throw error;
    }
  }
}
