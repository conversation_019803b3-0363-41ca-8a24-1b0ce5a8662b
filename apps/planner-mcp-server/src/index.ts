#!/usr/bin/env node

import dotenv from 'dotenv';
import { PlannerMCPServer } from './server.js';
import { logger } from './utils/logger.js';

// Load environment variables
dotenv.config();

/**
 * Main entry point for the Planner MCP Server
 */
async function main() {
  try {
    logger.info('Starting Planner MCP Server application...');

    // Create and start the server
    const server = new PlannerMCPServer();
    await server.start();

    // Handle graceful shutdown
    const shutdown = async (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      try {
        await server.stop();
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown', { error });
        process.exit(1);
      }
    };

    // Register signal handlers
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', { error });
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
      process.exit(1);
    });

    logger.info('Planner MCP Server is running...');

  } catch (error) {
    logger.error('Failed to start Planner MCP Server', { error });
    process.exit(1);
  }
}

// Start the application
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    logger.error('Application startup failed', { error });
    process.exit(1);
  });
}

export { PlannerMCPServer } from './server.js';
export * from './types/index.js';
export * from './services/index.js';
export * from './utils/index.js';
