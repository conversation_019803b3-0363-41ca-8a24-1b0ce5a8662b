import { z } from 'zod';

/**
 * Task priority enum
 */
export const TaskPrioritySchema = z.enum(['high', 'medium', 'low']);

/**
 * Task status enum
 */
export const TaskStatusSchema = z.enum(['pending', 'in_progress', 'completed', 'failed']);

/**
 * SubTask schema
 */
export const SubTaskSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  status: TaskStatusSchema,
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Task schema
 */
export const TaskSchema = z.object({
  id: z.string(),
  threadId: z.string(),
  title: z.string(),
  description: z.string(),
  details: z.string(),
  prompt: z.string(),
  testStrategy: z.string(),
  priority: TaskPrioritySchema,
  status: TaskStatusSchema,
  dependencies: z.array(z.string()),
  subtasks: z.array(SubTaskSchema),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Task types
 */
export type Task = z.infer<typeof TaskSchema>;
export type SubTask = z.infer<typeof SubTaskSchema>;
export type TaskPriority = z.infer<typeof TaskPrioritySchema>;
export type TaskStatus = z.infer<typeof TaskStatusSchema>;

/**
 * Task creation input
 */
export const CreateTaskSchema = z.object({
  threadId: z.string(),
  title: z.string(),
  description: z.string(),
  details: z.string().optional().default(''),
  prompt: z.string().optional().default(''),
  testStrategy: z.string().optional().default(''),
  priority: TaskPrioritySchema.optional().default('medium'),
  dependencies: z.array(z.string()).optional().default([]),
});

/**
 * Task update input
 */
export const UpdateTaskSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  details: z.string().optional(),
  prompt: z.string().optional(),
  testStrategy: z.string().optional(),
  priority: TaskPrioritySchema.optional(),
  status: TaskStatusSchema.optional(),
  dependencies: z.array(z.string()).optional(),
});

/**
 * Task status update input
 */
export const UpdateTaskStatusSchema = z.object({
  taskId: z.string(),
  status: TaskStatusSchema,
  notes: z.string().optional(),
});

/**
 * Task filter options
 */
export const TaskFilterSchema = z.object({
  threadId: z.string().optional(),
  status: TaskStatusSchema.optional(),
  priority: TaskPrioritySchema.optional(),
  search: z.string().optional(),
});

export type CreateTaskInput = z.infer<typeof CreateTaskSchema>;
export type UpdateTaskInput = z.infer<typeof UpdateTaskSchema>;
export type UpdateTaskStatusInput = z.infer<typeof UpdateTaskStatusSchema>;
export type TaskFilter = z.infer<typeof TaskFilterSchema>;
