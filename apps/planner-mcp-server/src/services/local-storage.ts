import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';
import { FileManager } from '../utils/file-manager.js';
import {
  Thread,
  Task,
  ThreadState,
  CreateTaskInput,
  UpdateTaskInput,
  TaskFilter,
  TaskSchema,
  ThreadSchema,
} from '../types/index.js';

/**
 * Metadata for local storage
 */
interface ThreadMetadata {
  threadId: string;
  lastSyncAt: string;
  lastModifiedAt: string;
  version: number;
}

/**
 * Local storage service for managing thread and task data
 */
export class LocalStorageService {
  private static instance: LocalStorageService;
  private fileManager: FileManager;

  private constructor() {
    this.fileManager = FileManager.getInstance();
    logger.info('LocalStorageService initialized');
  }

  public static getInstance(): LocalStorageService {
    if (!LocalStorageService.instance) {
      LocalStorageService.instance = new LocalStorageService();
    }
    return LocalStorageService.instance;
  }

  /**
   * Get thread metadata
   */
  private async getThreadMetadata(threadId: string): Promise<ThreadMetadata | null> {
    const metadataPath = this.fileManager.getMetadataFilePath(threadId);
    return this.fileManager.readJsonFile<ThreadMetadata>(metadataPath);
  }

  /**
   * Update thread metadata
   */
  private async updateThreadMetadata(threadId: string, updates: Partial<ThreadMetadata>): Promise<void> {
    const metadataPath = this.fileManager.getMetadataFilePath(threadId);
    const existing = await this.getThreadMetadata(threadId) || {
      threadId,
      lastSyncAt: new Date().toISOString(),
      lastModifiedAt: new Date().toISOString(),
      version: 1,
    };

    const updated: ThreadMetadata = {
      ...existing,
      ...updates,
      lastModifiedAt: new Date().toISOString(),
      version: existing.version + 1,
    };

    await this.fileManager.writeJsonFile(metadataPath, updated);
  }

  /**
   * Check if thread exists locally
   */
  public async threadExists(threadId: string): Promise<boolean> {
    return this.fileManager.threadExists(threadId);
  }

  /**
   * Ensure thread directory exists
   */
  public async ensureThreadDirectory(threadId: string): Promise<void> {
    return this.fileManager.ensureThreadDirectory(threadId);
  }

  /**
   * Get thread state
   */
  public async getThreadState(threadId: string): Promise<ThreadState | null> {
    const statePath = this.fileManager.getStateFilePath(threadId);
    return this.fileManager.readJsonFile<ThreadState>(statePath);
  }

  /**
   * Save thread state
   */
  public async saveThreadState(threadId: string, state: ThreadState): Promise<void> {
    await this.fileManager.ensureThreadDirectory(threadId);
    const statePath = this.fileManager.getStateFilePath(threadId);
    await this.fileManager.writeJsonFile(statePath, state);
    await this.updateThreadMetadata(threadId, {});
    logger.info(`Saved thread state for: ${threadId}`);
  }

  /**
   * Get all tasks for a thread
   */
  public async getTasks(threadId: string): Promise<Task[]> {
    const tasksPath = this.fileManager.getTasksFilePath(threadId);
    const tasksData = await this.fileManager.readJsonFile<{ tasks: Task[] }>(tasksPath);
    
    if (!tasksData || !tasksData.tasks) {
      return [];
    }

    // Validate tasks data
    try {
      return tasksData.tasks.map(task => TaskSchema.parse(task));
    } catch (error) {
      logger.error(`Invalid task data in thread ${threadId}`, { error });
      return [];
    }
  }

  /**
   * Save tasks for a thread
   */
  public async saveTasks(threadId: string, tasks: Task[]): Promise<void> {
    await this.fileManager.ensureThreadDirectory(threadId);
    const tasksPath = this.fileManager.getTasksFilePath(threadId);
    
    // Validate tasks before saving
    const validatedTasks = tasks.map(task => TaskSchema.parse(task));
    
    await this.fileManager.writeJsonFile(tasksPath, { tasks: validatedTasks });
    await this.updateThreadMetadata(threadId, {});
    logger.info(`Saved ${tasks.length} tasks for thread: ${threadId}`);
  }

  /**
   * Get a specific task
   */
  public async getTask(threadId: string, taskId: string): Promise<Task | null> {
    const tasks = await this.getTasks(threadId);
    return tasks.find(task => task.id === taskId) || null;
  }

  /**
   * Add a new task
   */
  public async addTask(threadId: string, taskInput: CreateTaskInput): Promise<Task> {
    const tasks = await this.getTasks(threadId);
    
    const newTask: Task = {
      id: uuidv4(),
      ...taskInput,
      status: 'pending',
      subtasks: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Validate the new task
    const validatedTask = TaskSchema.parse(newTask);
    
    tasks.push(validatedTask);
    await this.saveTasks(threadId, tasks);
    
    logger.info(`Added new task: ${validatedTask.id} to thread: ${threadId}`);
    return validatedTask;
  }

  /**
   * Update an existing task
   */
  public async updateTask(threadId: string, taskId: string, updates: UpdateTaskInput): Promise<Task | null> {
    const tasks = await this.getTasks(threadId);
    const taskIndex = tasks.findIndex(task => task.id === taskId);
    
    if (taskIndex === -1) {
      logger.warn(`Task not found: ${taskId} in thread: ${threadId}`);
      return null;
    }

    const updatedTask: Task = {
      ...tasks[taskIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    } as Task;

    // Validate the updated task
    const validatedTask = TaskSchema.parse(updatedTask);
    
    tasks[taskIndex] = validatedTask;
    await this.saveTasks(threadId, tasks);
    
    logger.info(`Updated task: ${taskId} in thread: ${threadId}`);
    return validatedTask;
  }

  /**
   * Delete a task
   */
  public async deleteTask(threadId: string, taskId: string): Promise<boolean> {
    const tasks = await this.getTasks(threadId);
    const initialLength = tasks.length;
    const filteredTasks = tasks.filter(task => task.id !== taskId);
    
    if (filteredTasks.length === initialLength) {
      logger.warn(`Task not found for deletion: ${taskId} in thread: ${threadId}`);
      return false;
    }

    await this.saveTasks(threadId, filteredTasks);
    logger.info(`Deleted task: ${taskId} from thread: ${threadId}`);
    return true;
  }

  /**
   * Filter tasks based on criteria
   */
  public async filterTasks(threadId: string, filter: TaskFilter): Promise<Task[]> {
    const tasks = await this.getTasks(threadId);
    
    return tasks.filter(task => {
      if (filter.status && task.status !== filter.status) return false;
      if (filter.priority && task.priority !== filter.priority) return false;
      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        const matchesSearch = 
          task.title.toLowerCase().includes(searchLower) ||
          task.description.toLowerCase().includes(searchLower) ||
          task.details.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }
      return true;
    });
  }

  /**
   * Get thread metadata information
   */
  public async getThreadInfo(threadId: string): Promise<ThreadMetadata | null> {
    return this.getThreadMetadata(threadId);
  }

  /**
   * List all local threads
   */
  public async listThreads(): Promise<string[]> {
    return this.fileManager.listThreads();
  }

  /**
   * Delete a thread and all its data
   */
  public async deleteThread(threadId: string): Promise<void> {
    await this.fileManager.deleteThread(threadId);
    logger.info(`Deleted thread: ${threadId}`);
  }

  /**
   * Get full thread data (state + tasks)
   */
  public async getThread(threadId: string): Promise<Thread | null> {
    if (!await this.threadExists(threadId)) {
      return null;
    }

    const state = await this.getThreadState(threadId);
    const tasks = await this.getTasks(threadId);
    const metadata = await this.getThreadMetadata(threadId);

    if (!state || !metadata) {
      return null;
    }

    const thread: Thread = {
      id: threadId,
      state,
      tasks,
      createdAt: metadata.lastSyncAt,
      updatedAt: metadata.lastModifiedAt,
    };

    return ThreadSchema.parse(thread);
  }

  /**
   * Save full thread data
   */
  public async saveThread(thread: Thread): Promise<void> {
    const validatedThread = ThreadSchema.parse(thread);
    
    await this.saveThreadState(validatedThread.id, validatedThread.state);
    await this.saveTasks(validatedThread.id, validatedThread.tasks);
    
    logger.info(`Saved complete thread: ${validatedThread.id}`);
  }
}
