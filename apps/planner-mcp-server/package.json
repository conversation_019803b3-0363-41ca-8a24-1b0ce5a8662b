{"name": "planner-mcp-server", "version": "1.0.0", "description": "MCP server for task management and synchronization with remote API", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "keywords": ["mcp", "task-management", "synchronization", "typescript"], "author": "Task Master AI", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "fastmcp": "^1.20.5", "axios": "^1.6.0", "fs-extra": "^11.2.0", "uuid": "^9.0.1", "zod": "^3.23.8", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/uuid": "^9.0.7", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.0", "typescript": "^5.3.0", "tsx": "^4.16.2", "jest": "^29.7.0", "ts-jest": "^29.1.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0"}, "engines": {"node": ">=18.0.0"}}