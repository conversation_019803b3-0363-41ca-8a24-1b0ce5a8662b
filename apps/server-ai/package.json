{"name": "@airstar/server-ai", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "export NODE_ENV=development && nest start --watch", "start:dev": "export NODE_ENV=development && nest start --watch", "start:debug": "export NODE_ENV=development && nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -P ./tsconfig.json ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- -d src/data-source.ts migration:generate", "migration:run": "npm run typeorm -- -d src/data-source.ts migration:run", "migration:revert": "npm run typeorm -- -d src/data-source.ts migration:revert", "migration:show": "npm run typeorm -- -d src/data-source.ts migration:show", "migration:create": "npm run typeorm -- migration:create"}, "dependencies": {"@infra-node/kconf": "^1.1.17", "@infra-node/redis": "^1.3.3", "@langchain/community": "^0.3.44", "@langchain/core": "^0.3.57", "@langchain/langgraph": "^0.2.73", "@langchain/mcp-adapters": "^0.4.5", "@langchain/openai": "^0.5.11", "@langchain/tavily": "^0.1.2", "@modelcontextprotocol/sdk": "^1.11.5", "@nestjs/common": "^10.0.0", "@nestjs/config": "^1.0.3", "@nestjs/core": "^10.0.0", "@nestjs/elasticsearch": "^10.0.1", "@nestjs/platform-express": "^10.4.1", "@nestjs/typeorm": "^10.0.2", "@types/isomorphic-fetch": "^0.0.35", "aws-sdk": "^2.1692.0", "axios": "^1.6.7", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.1", "duck-duck-scrape": "^2.2.7", "eventsource-parser": "^3.0.2", "express": "^4.19.2", "isomorphic-fetch": "^3.0.0", "js-yaml": "^4.1.0", "langchain": "^0.3.27", "mysql2": "^3.11.0", "node-fetch": "^3.1.0", "openai": "^4.102.0", "ramda": "^0.29.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "uuid": "3.4.0", "zod": "^3.25.20"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/js-yaml": "^4.0.9", "@types/node": "^20.3.1", "@types/ramda": "^0.30.2", "@types/supertest": "^2.0.12", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}