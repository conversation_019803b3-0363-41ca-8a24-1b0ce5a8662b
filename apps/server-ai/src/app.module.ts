import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';

import { GlobalModule } from './global/global.module';
import { KwaipilotModule } from './kwaipilot/kwaipilot.module';
import { ChatModule } from './chat/chat.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { yamlConfigLoad } from './environment/load';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UploadFileModule } from './upload-file/upload-file.module';

import { KconfService } from './global/kconf/kconf.service';

import { PlanModule } from './plan/plan.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [yamlConfigLoad],
      isGlobal: true,
    }),

    TypeOrmModule.forRootAsync({
      useFactory: async (config: ConfigService) => {
        const mysqlJson = config.get('db.mysql');
        return {
          type: mysqlJson.type,
          host: mysqlJson.host, // 替换为你的 MySQL 主机地址
          port: mysqlJson.port, // 替换为你的 MySQL 端口号
          username: mysqlJson.username, // 替换为你的 MySQL 用户名
          password: mysqlJson.password, // 替换为你的 MySQL 密码
          database: mysqlJson.database, // 替换为你的数据库名称
          // 确保能够找到所有实体，包括planner模块中的实体
          entities: [__dirname + '/**/*.entity{.ts,.js}'],
          // 根据环境决定是否自动同步数据库结构
          synchronize:
            process.env.NODE_ENV === 'local' ||
            process.env.NODE_ENV === 'development',
          // 添加额外的连接选项
          logging: false,
          autoLoadEntities: true, // 自动加载通过forFeature注册的实体
        };
      },
      inject: [ConfigService],
    }), // 加载 TypeORM 配置
    GlobalModule,
    KwaipilotModule,
    ChatModule,
    UploadFileModule,
    PlanModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: KconfService,
      useClass: KconfService,
    },
  ],
})
export class AppModule {}
