import { DataSource } from 'typeorm';
import { yamlConfigLoad } from './environment/load';

// 加载配置
const config = yamlConfigLoad();
const mysqlConfig = (config as any).db?.mysql;

if (!mysqlConfig) {
  throw new Error('MySQL configuration not found');
}

export const AppDataSource = new DataSource({
  type: mysqlConfig.type,
  host: mysqlConfig.host,
  port: mysqlConfig.port,
  username: mysqlConfig.username,
  password: mysqlConfig.password,
  database: mysqlConfig.database,
  synchronize: false, // 生产环境禁用自动同步
  logging: process.env.NODE_ENV === 'development',
  entities: [__dirname + '/**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/migrations/*{.ts,.js}'],
  migrationsTableName: 'migrations',
  ssl:
    process.env.NODE_ENV === 'production'
      ? { rejectUnauthorized: false }
      : false,
});
