import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddInterruptStatusToThreads1700000003000
  implements MigrationInterface
{
  name = 'AddInterruptStatusToThreads1700000003000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加interrupt_status字段到threads表
    await queryRunner.addColumn(
      'threads',
      new TableColumn({
        name: 'interrupt_status',
        type: 'int',
        default: 0,
        comment:
          'Interrupt status: 0=NONE, 1=WAITING_FEEDBACK, 2=FEEDBACK_RECEIVED',
      }),
    );

    // 添加interrupt_data字段到threads表
    await queryRunner.addColumn(
      'threads',
      new TableColumn({
        name: 'interrupt_data',
        type: 'json',
        isNullable: true,
        comment: 'Data associated with the interrupt',
      }),
    );

    // 添加interrupt_at字段到threads表
    await queryRunner.addColumn(
      'threads',
      new TableColumn({
        name: 'interrupt_at',
        type: 'datetime',
        isNullable: true,
        comment: 'Timestamp when the interrupt occurred',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除添加的字段
    await queryRunner.dropColumn('threads', 'interrupt_at');
    await queryRunner.dropColumn('threads', 'interrupt_data');
    await queryRunner.dropColumn('threads', 'interrupt_status');
  }
}
