import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialPlanTables1700000000000 implements MigrationInterface {
  name = 'InitialPlanTables1700000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 检查threads表是否已存在，如果不存在则创建
    const threadsTableExists = await queryRunner.hasTable('threads');
    if (!threadsTableExists) {
      await queryRunner.query(`
        CREATE TABLE \`threads\` (
          \`id\` varchar(36) NOT NULL,
          \`user_id\` varchar(255) DEFAULT NULL,
          \`title\` varchar(255) NOT NULL,
          \`prdLink\` varchar(500) DEFAULT NULL,
          \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          \`metadata\` json DEFAULT NULL,
          \`status\` int NOT NULL DEFAULT '1',
          \`messages\` json NOT NULL DEFAULT ('[]'),
          \`state\` json DEFAULT NULL,
          \`tasks\` json NOT NULL DEFAULT ('[]'),
          PRIMARY KEY (\`id\`),
          KEY \`IDX_THREAD_USER_ID\` (\`user_id\`),
          KEY \`IDX_THREAD_STATUS\` (\`status\`),
          KEY \`IDX_THREAD_CREATED_AT\` (\`created_at\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
    }

    // 创建messages表
    await queryRunner.query(`
      CREATE TABLE \`messages\` (
        \`id\` varchar(36) NOT NULL,
        \`threadId\` varchar(36) NOT NULL,
        \`type\` enum('user','assistant','system','interrupt','tools') NOT NULL DEFAULT 'user',
        \`content\` text NOT NULL,
        \`status\` int NOT NULL DEFAULT '1',
        \`metadata\` json DEFAULT NULL,
        \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        KEY \`IDX_MESSAGE_threadId\` (\`threadId\`),
        KEY \`IDX_MESSAGE_TYPE\` (\`type\`),
        KEY \`IDX_MESSAGE_STATUS\` (\`status\`),
        KEY \`IDX_MESSAGE_CREATED_AT\` (\`created_at\`),
        CONSTRAINT \`FK_MESSAGE_THREAD\` FOREIGN KEY (\`threadId\`) REFERENCES \`threads\` (\`id\`) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建tasks表
    await queryRunner.query(`
      CREATE TABLE \`tasks\` (
        \`id\` varchar(36) NOT NULL,
        \`threadId\` varchar(36) NOT NULL,
        \`title\` varchar(255) NOT NULL,
        \`description\` text DEFAULT NULL,
        \`type\` enum('frontend','backend','integration') NOT NULL DEFAULT 'frontend',
        \`status\` enum('pending','in_progress','completed','deleted') NOT NULL DEFAULT 'pending',
        \`priority\` int NOT NULL DEFAULT '0',
        \`progress\` int NOT NULL DEFAULT '0',
        \`dependencies\` json DEFAULT NULL,
        \`metadata\` json DEFAULT NULL,
        \`completion_notes\` text DEFAULT NULL,
        \`deployment_url\` varchar(500) DEFAULT NULL,
        \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        KEY \`IDX_TASK_threadId\` (\`threadId\`),
        KEY \`IDX_TASK_TYPE\` (\`type\`),
        KEY \`IDX_TASK_STATUS\` (\`status\`),
        KEY \`IDX_TASK_PRIORITY\` (\`priority\`),
        KEY \`IDX_TASK_CREATED_AT\` (\`created_at\`),
        CONSTRAINT \`FK_TASK_THREAD\` FOREIGN KEY (\`threadId\`) REFERENCES \`threads\` (\`id\`) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除表（外键约束会自动处理）
    await queryRunner.query(`DROP TABLE IF EXISTS \`tasks\``);
    await queryRunner.query(`DROP TABLE IF EXISTS \`messages\``);

    // 注意：这里不删除threads表，因为它可能在其他地方使用
    // 如果需要删除threads表，取消以下注释
    // await queryRunner.query(`DROP TABLE IF EXISTS \`threads\``);
  }
}
