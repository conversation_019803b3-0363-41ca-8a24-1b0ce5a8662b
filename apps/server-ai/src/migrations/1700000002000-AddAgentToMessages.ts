import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddAgentToMessages1700000002000 implements MigrationInterface {
  name = 'AddAgentToMessages1700000002000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加agent字段到messages表
    await queryRunner.addColumn(
      'messages',
      new TableColumn({
        name: 'agent',
        type: 'enum',
        enum: [
          'coordinator',
          'planner',
          'researcher',
          'coder',
          'analyst',
          'reporter',
        ],
        isNullable: true,
        comment: 'Agent type that generated this message',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除agent字段
    await queryRunner.dropColumn('messages', 'agent');
  }
}
