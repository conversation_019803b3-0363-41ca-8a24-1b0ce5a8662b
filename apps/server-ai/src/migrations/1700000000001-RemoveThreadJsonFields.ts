import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveThreadJsonFields1700000000001 implements MigrationInterface {
  name = 'RemoveThreadJsonFields1700000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 备份数据：将 JSON 字段中的数据迁移到对应的关系表中（如果需要的话）
    // 注意：在实际环境中，你可能需要先确保所有数据都已经正确迁移到关系表中

    // 删除 threads 表中的 messages 和 tasks JSON 字段
    await queryRunner.query(`
      ALTER TABLE \`threads\`
      DROP COLUMN \`messages\`,
      DROP COLUMN \`tasks\`
    `);

    console.log('✅ 已删除 threads 表中的 messages 和 tasks JSON 字段');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚操作：重新添加 JSON 字段
    await queryRunner.query(`
      ALTER TABLE \`threads\`
      ADD COLUMN \`messages\` json NOT NULL DEFAULT ('[]'),
      ADD COLUMN \`tasks\` json NOT NULL DEFAULT ('[]')
    `);

    console.log('✅ 已恢复 threads 表中的 messages 和 tasks JSON 字段');
  }
}
