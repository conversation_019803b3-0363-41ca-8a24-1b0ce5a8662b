import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddThreadsIndex1700000001000 implements MigrationInterface {
  name = 'AddThreadsIndex1700000001000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加复合索引：status + updated_at，这样在查询时可以避免排序内存问题
    await queryRunner.query(`
      CREATE INDEX \`IDX_THREAD_STATUS_UPDATED_AT\` ON \`threads\` (\`status\`, \`updated_at\` DESC)
    `);

    // 如果需要支持用户查询，也可以添加这个索引
    await queryRunner.query(`
      CREATE INDEX \`IDX_THREAD_USER_STATUS_UPDATED\` ON \`threads\` (\`user_id\`, \`status\`, \`updated_at\` DESC)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚时删除索引
    await queryRunner.query(
      `DROP INDEX \`IDX_THREAD_STATUS_UPDATED_AT\` ON \`threads\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_THREAD_USER_STATUS_UPDATED\` ON \`threads\``,
    );
  }
}
