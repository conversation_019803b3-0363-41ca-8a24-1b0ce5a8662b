import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';

export enum ThreadStatus {
  DELETED = 0,
  PROCESSING = 1,
  COMPLETED = 2,
}

// 新增：中断状态枚举
export enum InterruptStatus {
  NONE = 0, // 无中断
  WAITING_FEEDBACK = 1, // 等待用户反馈
  FEEDBACK_RECEIVED = 2, // 已收到反馈
}

@Entity('threads')
export class Thread {
  @PrimaryColumn()
  id: string;

  @Column({ nullable: true })
  user_id: string;

  @Column()
  title: string;

  @Column({ nullable: true })
  prdLink: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({
    type: 'int',
    default: ThreadStatus.PROCESSING,
  })
  status: ThreadStatus;

  @Column({ type: 'json', nullable: true })
  state: Record<string, any>;

  // 新增：中断状态字段
  @Column({
    type: 'int',
    default: InterruptStatus.NONE,
  })
  interrupt_status: InterruptStatus;

  // 新增：中断数据字段，存储中断时的具体信息
  @Column({ type: 'json', nullable: true })
  interrupt_data: Record<string, any>;

  // 新增：中断时间戳
  @Column({ nullable: true })
  interrupt_at: Date;

  // 关联关系 - 使用字符串引用避免循环依赖
  @OneToMany('Message', 'thread', { eager: false })
  messages: any[];

  @OneToMany('Task', 'thread', { eager: false })
  tasks: any[];
}
