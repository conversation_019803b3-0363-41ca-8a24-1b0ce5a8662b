import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Thread } from './thread.entity';

export enum TaskType {
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  INTEGRATION = 'integration',
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  DELETED = 'deleted',
}

@Entity('tasks')
export class Task {
  @PrimaryColumn()
  id: string;

  @Column()
  threadId: string;

  @Column()
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: TaskType,
    default: TaskType.FRONTEND,
  })
  type: TaskType;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.PENDING,
  })
  status: TaskStatus;

  @Column({ type: 'int', default: 0 })
  priority: number;

  @Column({ type: 'int', default: 0 })
  progress: number;

  @Column({ type: 'json', nullable: true })
  dependencies: string[];

  @Column({ type: 'text', nullable: true })
  completion_notes: string;

  @Column({ nullable: true })
  deployment_url: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => Thread, (thread) => thread.tasks)
  @JoinColumn({ name: 'threadId' })
  thread: Thread;
}
