import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  HttpException,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import { MessageService } from './message.service';
import { Message } from './message.entity';
import {
  GetMessagesDto,
  AddMessageDto,
  UpdateMessageDto,
  DeleteMessageDto,
} from './message.dto';
import { TransformInterceptor } from 'src/utils/transform.interceptor';

@Controller('plan')
@UseInterceptors(TransformInterceptor)
export class MessageController {
  constructor(private readonly messageService: MessageService) {}

  @Get('getMessages')
  async getMessages(@Query() getMessagesDto: GetMessagesDto) {
    try {
      return await this.messageService.getMessages(getMessagesDto);
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `获取消息列表失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('addMessage')
  async addMessage(@Body() addMessageDto: AddMessageDto): Promise<Message> {
    try {
      const message = await this.messageService.addMessage(addMessageDto);
      return message;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `添加消息失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('updateMessage')
  async updateMessage(
    @Body() updateMessageDto: UpdateMessageDto,
  ): Promise<Message> {
    try {
      const message = await this.messageService.updateMessage(updateMessageDto);
      return message;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `更新消息失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('deleteMessage')
  async deleteMessage(
    @Body() deleteMessageDto: DeleteMessageDto,
  ): Promise<boolean> {
    try {
      return await this.messageService.deleteMessage(
        deleteMessageDto.messageId,
      );
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `删除消息失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
