import {
  Controller,
  Post,
  Body,
  Sse,
  HttpCode,
  HttpException,
  HttpStatus,
  UseInterceptors,
  Get,
  Param,
} from '@nestjs/common';
import {
  IsArray,
  IsString,
  IsOptional,
  IsObject,
  IsNotEmpty,
  IsNumber,
} from 'class-validator';
import { v4 as uuidv4 } from 'uuid';
import graph from './graph/graph';
import { ThreadService } from './thread.service';
import { MessageService } from './message.service';
import { TaskService } from './task.service';
import { MessageType } from './message.entity';
import { Observable, Observer } from 'rxjs';
import { TransformInterceptor } from 'src/utils/transform.interceptor';
import { ChatMessage } from '@langchain/core/messages';
import { Command } from '@langchain/langgraph';
import { AgentType } from './graph/constants';
import { NodeNames } from './graph/constants';
import { InterruptStatus } from './thread.entity';
import { ChatEventType } from './types/event-types';

export class ChatMessageDto {
  role: string;

  content: string;
}

export class ChatRequestDto {
  @IsArray()
  messages: ChatMessageDto[];

  @IsString()
  @IsOptional()
  threadId: string = '__default__';

  @IsString()
  @IsOptional()
  interruptFeedback: string = '';

  @IsObject()
  @IsOptional()
  mcpSettings: any = {};
}

export class RegenerateRequestDto {
  @IsString()
  @IsNotEmpty()
  threadId: string;

  @IsNumber()
  @IsNotEmpty()
  messageIndex: number;
}

// 定义正确的MessageEvent接口
interface MessageEvent {
  data: string | Record<string, any>;
  type?: string;
  id?: string;
  retry?: number;
}

// 定义 chunk 处理相关的接口
interface ProcessedChunk {
  type: ChatEventType;
  threadId: string;
  agent?: string;
  content?: string;
  role?: string;
  finish_reason?: string;
  id?: string;
}

@Controller('plan')
@UseInterceptors(TransformInterceptor)
export class ChatController {
  constructor(
    private readonly threadService: ThreadService,
    private readonly messageService: MessageService,
    private readonly taskService: TaskService,
  ) {}

  @Post('stream')
  @HttpCode(200)
  @Sse()
  async chatStream(
    @Body() request: ChatRequestDto,
  ): Promise<Observable<MessageEvent>> {
    let { threadId } = request;
    const { messages, interruptFeedback } = request;
    // 如果没有提供threadId，生成一个新的
    if (threadId === '__default__') {
      threadId = uuidv4();
    }

    // 创建流式响应
    return new Observable<MessageEvent>((observer) => {
      this.processWorkflowStream(
        messages,
        threadId,
        observer,
        interruptFeedback,
      );
    });
  }

  @Post('stream/regenerate')
  @HttpCode(200)
  @Sse()
  async regenerateResponse(
    @Body() request: RegenerateRequestDto,
  ): Promise<Observable<MessageEvent>> {
    const { threadId, messageIndex } = request;

    try {
      // 获取会话信息
      const thread = await this.threadService.getThread(threadId);
      if (!thread) {
        throw new HttpException(
          {
            status: HttpStatus.NOT_FOUND,
            error: `Thread ${threadId} not found`,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // 获取会话中的消息
      const messages = thread.messages || [];
      if (messageIndex >= messages.length) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            error: `Message index ${messageIndex} out of range`,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // 截取到指定消息的历史记录
      const historyMessages = messages.slice(0, messageIndex + 1);

      // 创建流式响应
      return new Observable<MessageEvent>((observer) => {
        this.processWorkflowStream(
          historyMessages,
          threadId,
          observer,
          'resume',
        );
      });
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `Regenerate failed: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('thread/:threadId/interrupt-status')
  @HttpCode(200)
  async getInterruptStatus(@Param('threadId') threadId: string) {
    try {
      const interruptInfo = await this.threadService.getInterruptInfo(threadId);

      if (!interruptInfo) {
        throw new HttpException(
          {
            status: HttpStatus.NOT_FOUND,
            error: `Thread ${threadId} not found`,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        threadId,
        interruptStatus: interruptInfo.status,
        interruptData: interruptInfo.data,
        interruptAt: interruptInfo.interruptAt,
        isWaitingFeedback:
          interruptInfo.status === InterruptStatus.WAITING_FEEDBACK,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `Failed to get interrupt status: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 处理工作流流式响应 - 重构版本
   */
  private async processWorkflowStream(
    messages: any[],
    threadId: string,
    observer: Observer<MessageEvent>,
    interruptFeedback: string,
  ): Promise<void> {
    try {
      // 新增：如果有中断反馈，清除中断状态
      if (interruptFeedback && interruptFeedback !== 'resume') {
        try {
          await this.threadService.clearInterruptStatus(threadId);
        } catch (error) {
          console.error('Failed to clear interrupt status:', error);
        }
      }

      // 处理interrupt feedback或正常流程
      const streamInput = this.determineStreamInput(
        messages,
        interruptFeedback,
      );

      const streamGenerator = await graph.stream(streamInput, {
        configurable: {
          thread_id: threadId,
        },
        streamMode: ['updates', 'messages'],
      });

      // 存储所有消息和状态
      const allMessages = [...messages];
      let currentState = {};

      // 按消息ID聚合消息内容和agent信息
      const messageAggregation = new Map<
        string,
        {
          content: string;
          agent: AgentType;
          type: string;
          metadata: any;
        }
      >();

      for await (const chunk of streamGenerator) {
        const [streamMode, messages] = chunk;
        const agentName = messages[messages.length - 1]?.langgraph_node;
        try {
          if (streamMode === 'updates') {
            // 获取并保存当前状态
            currentState = await graph.getState({
              configurable: {
                thread_id: threadId,
              },
            });
          }
          // 使用新的 chunk 处理逻辑
          const chunkObj = this.extractChunkObject(chunk);
          const processedChunk = await this.processStreamChunk(
            chunkObj,
            agentName,
            threadId,
          );

          if (processedChunk) {
            // 按消息ID聚合内容
            if (
              processedChunk.id &&
              processedChunk.type === ChatEventType.MESSAGE_CHUNK
            ) {
              const messageId = processedChunk.id;
              const existing = messageAggregation.get(messageId) || {
                content: '',
                agent:
                  (processedChunk.agent as AgentType) || AgentType.COORDINATOR,
                type: processedChunk.type,
                metadata: {
                  role: processedChunk.role || 'assistant',
                  fromWorkflow: true,
                  timestamp: new Date().toISOString(),
                },
              };

              // 累积内容
              existing.content += processedChunk.content || '';
              // 更新agent信息（如果有新的agent信息）
              if (processedChunk.agent) {
                existing.agent = processedChunk.agent as AgentType;
              }

              messageAggregation.set(messageId, existing);
            }

            // 保存消息到 allMessages（保持原有格式兼容性）
            const messageToSave = this.convertToLegacyFormat(chunkObj);
            if (messageToSave) {
              allMessages.push(messageToSave);
            }

            // 发送处理后的事件
            const messageEvent = this.createMessageEvent(processedChunk);
            observer.next(messageEvent);
          }
        } catch (chunkError) {
          // 发送错误事件但继续处理
          observer.next({
            type: ChatEventType.ERROR,
            data: {
              threadId,
              error: `Chunk processing error: ${chunkError.message}`,
            },
          });
        }
      }

      // 在流结束前，保存所有聚合的消息到数据库
      await this.saveAggregatedMessages(
        threadId,
        allMessages,
        currentState,
        messageAggregation,
        observer,
      );

      observer.complete();
    } catch (error) {
      console.error('Error in processWorkflowStream:', error);
      observer.next({
        type: ChatEventType.ERROR,
        data: {
          threadId,
          error: error.message || 'Stream processing failed',
        },
      });
      observer.complete();
    }
  }

  /**
   * 确定流式输入类型
   */
  private determineStreamInput(
    messages: ChatMessage[],
    interruptFeedback: string,
  ): Command | Record<string, any> {
    // 处理interrupt feedback
    if (interruptFeedback) {
      const resumeMsg = this.buildResumeMessage(messages, interruptFeedback);
      return new Command({ resume: resumeMsg });
    }
    // 正常流程 - 构造初始状态
    return {
      originalPRD:
        messages && messages.length > 0
          ? messages[messages.length - 1].content
          : '',
    };
  }
  /**
   * 构建恢复消息
   */
  private buildResumeMessage(
    messages: ChatMessage[],
    interruptFeedback: string,
  ) {
    const resumeMsg = { feedback: interruptFeedback };

    return resumeMsg;
  }

  /**
   * 提取 chunk 对象 - 新增方法
   */
  private extractChunkObject(chunk: any): any {
    if (Array.isArray(chunk) && chunk.length > 0) {
      const msg = chunk[chunk.length - 1];
      if (Array.isArray(msg)) {
        return msg[0] || null;
      }
      return msg ?? null;
    }
    return Array.isArray(chunk) ? chunk[0] : chunk;
  }

  /**
   * 处理流式响应块 - 新增方法
   */
  private async processStreamChunk(
    chunk: any,
    agentName: string,
    threadId: string,
  ): Promise<ProcessedChunk | null> {
    try {
      if (!chunk || typeof chunk !== 'object') {
        return null;
      }

      // 处理中断事件
      if (chunk.__interrupt__) {
        return await this.handleInterruptChunk(chunk, agentName, threadId);
      }

      // 处理普通消息内容
      return this.handleContentChunk(chunk, agentName, threadId);
    } catch (error) {
      console.error('Error processing stream chunk:', error);
      return {
        type: ChatEventType.ERROR,
        threadId,
        content: `Processing error: ${error.message}`,
      };
    }
  }

  /**
   * 处理中断事件 chunk - 新增方法
   */
  private async handleInterruptChunk(
    chunk: any,
    agentName: string,
    threadId: string,
  ): Promise<ProcessedChunk> {
    const interruptData = chunk.__interrupt__[0];

    const agent = this.getAgentFromNodeName(agentName);

    // 新增：保存中断状态到数据库
    try {
      await this.threadService.setInterruptStatus(
        threadId,
        InterruptStatus.WAITING_FEEDBACK,
        {
          agent,
          data: interruptData?.value || {},
          timestamp: new Date().toISOString(),
          chunkId: chunk.id,
        },
      );
    } catch (error) {
      console.error('Failed to save interrupt status:', error);
    }

    return {
      type: ChatEventType.INTERRUPT,
      threadId,
      id: this.generateId(chunk.id),
      agent,
      content: JSON.stringify(interruptData?.value || {}),
      role: 'assistant',
      finish_reason: 'interrupt',
    };
  }

  /**
   * 处理内容 chunk - 新增方法
   */
  private handleContentChunk(
    chunk: any,
    agentName: string,
    threadId: string,
  ): ProcessedChunk | null {
    const content = chunk?.kwargs?.content || chunk?.content || '';
    if (!content) {
      return null;
    }

    const agent = this.getAgentFromNodeName(agentName);

    return {
      type: ChatEventType.MESSAGE_CHUNK,
      threadId,
      id: this.generateId(chunk.id),
      agent,
      content,
      role: 'assistant',
    };
  }

  /**
   * 根据节点名称获取对应的 agent 类型 - 优化版本
   */
  private getAgentFromNodeName(nodeName: string): string {
    // 节点名称到Agent类型的映射表
    const nodeToAgentMap: Record<string, AgentType> = {
      [NodeNames.COORDINATOR]: AgentType.COORDINATOR,
      [NodeNames.PRD_PARSER]: AgentType.PLANNER,
      [NodeNames.PRD_FEEDBACK]: AgentType.PLANNER,
      [NodeNames.FRONTEND_PLANNER]: AgentType.PLANNER,
      [NodeNames.BACKEND_PLANNER]: AgentType.PLANNER,
      [NodeNames.PLANNER_FEEDBACK]: AgentType.PLANNER,
      [NodeNames.TOOLS]: AgentType.COORDINATOR,
    };

    return nodeToAgentMap[nodeName] || AgentType.COORDINATOR;
  }

  /**
   * 生成唯一ID - 新增方法
   */
  private generateId(chunkId?: string): string {
    return chunkId || uuidv4();
  }

  /**
   * 创建MessageEvent
   */
  private createMessageEvent(data: any): MessageEvent {
    // 清理空的content字段
    if (data.content === '') {
      delete data.content;
    }
    const type = data.type;
    delete data.type;
    return {
      type,
      data,
    } as MessageEvent;
  }

  /**
   * 转换为旧格式以保持兼容性 - 新增方法
   */
  private convertToLegacyFormat(chunk: any): any {
    if (!chunk) return null;

    // 保持与原有格式的兼容性
    return {
      ...chunk,
      kwargs: chunk.kwargs || { content: chunk.content },
    };
  }

  /**
   * 保存聚合的消息数据 - 更新版本
   */
  private async saveAggregatedMessages(
    threadId: string,
    allMessages: any[],
    currentState: any,
    messageAggregation: Map<
      string,
      {
        content: string;
        agent: AgentType;
        type: string;
        metadata: any;
      }
    >,
    observer: Observer<MessageEvent>,
  ): Promise<void> {
    try {
      // 检查会话是否存在
      const existingThread = await this.threadService.getThread(threadId);
      if (existingThread) {
        // 批量保存所有聚合的消息
        for (const [messageId, messageData] of messageAggregation) {
          if (messageData.content.trim()) {
            await this.messageService.addMessage({
              id: messageId, // 使用chunk返回的原始消息ID
              threadId,
              content: messageData.content,
              type: MessageType.ASSISTANT,
              agent: messageData.agent,
              metadata: {
                ...messageData.metadata,
                // 移除originalMessageId，因为现在直接使用原始ID
              },
            });
          }
        }

        if (
          currentState?.values.shouldSaveTasks &&
          currentState?.values.finalTasksJSON
        ) {
          try {
            const savedTasks = await this.taskService.saveTasksFromJSON(
              threadId,
              currentState.values.finalTasksJSON,
            );

            // 发送 task_complete 事件
            observer.next({
              type: ChatEventType.TASK_COMPLETE,
              data: {
                threadId,
                message: `任务规划已完成！共生成 ${savedTasks.length} 个任务`,
                tasks: savedTasks,
                timestamp: new Date().toISOString(),
              },
            });

            // 新增：发送最终完成事件
            observer.next({
              type: ChatEventType.END,
              data: {
                threadId,
                reason: 'completed',
                summary: `🎉 项目规划已全部完成！共生成 ${savedTasks.length} 个开发任务，包括前端、后端等模块。您可以在任务列表中查看详细信息并开始开发。`,
                results: {
                  totalTasks: savedTasks.length,
                  frontendTasks: savedTasks.filter(
                    (task) => task.type === 'frontend',
                  ).length,
                  backendTasks: savedTasks.filter(
                    (task) => task.type === 'backend',
                  ).length,
                  tasks: savedTasks,
                },
                nextActions: [
                  '查看任务列表详情',
                  '开始执行开发任务',
                  '设置项目环境和配置',
                ],
                timestamp: new Date().toISOString(),
              },
            });
          } catch (taskError) {
            observer.next({
              type: ChatEventType.ERROR,
              data: {
                threadId,
                error: `保存任务失败: ${taskError.message}`,
              },
            });
          }
        }

        // 更新现有会话的状态
        await this.threadService.updateThread(threadId, {
          state: currentState.values,
        });
      }
    } catch (dbError) {
      console.error('Database save error:', dbError);
      observer.next({
        type: ChatEventType.ERROR,
        data: {
          threadId,
          error: 'Failed to save thread data',
        },
      });
    }
  }

  /**
   * 保存线程数据 - 保留原方法用于向后兼容
   * @deprecated 使用 saveAggregatedMessages 替代
   */
  private async saveThreadData(
    threadId: string,
    allMessages: any[],
    currentState: any,
    aiMessageContent: string,
    currentAgent: AgentType,
    observer: Observer<MessageEvent>,
  ): Promise<void> {
    // 保留原有实现以防需要回退
    try {
      const existingThread = await this.threadService.getThread(threadId);
      if (existingThread) {
        if (aiMessageContent.trim()) {
          await this.messageService.addMessage({
            threadId,
            content: aiMessageContent,
            type: MessageType.ASSISTANT,
            agent: currentAgent,
            metadata: {
              role: 'assistant',
              fromWorkflow: true,
              timestamp: new Date().toISOString(),
            },
          });
        }

        await this.threadService.updateThread(threadId, {
          state: currentState,
        });
      }
    } catch (dbError) {
      console.error('Database save error:', dbError);
      observer.next({
        type: ChatEventType.ERROR,
        data: {
          threadId,
          error: 'Failed to save thread data',
        },
      });
    }
  }
}
