import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  HttpException,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import { TaskService } from './task.service';
import { Task } from './task.entity';
import {
  GetTasksDto,
  CreateTaskDto,
  UpdateTaskStatusDto,
  CompleteTaskDto,
  DeleteTaskNewDto,
  GenerateTasksDto,
} from './task.dto';
import { TransformInterceptor } from 'src/utils/transform.interceptor';

@Controller('plan')
@UseInterceptors(TransformInterceptor)
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @Get('getTasks')
  async getTasks(@Query() getTasksDto: GetTasksDto) {
    try {
      return await this.taskService.getTasks(getTasksDto);
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `获取任务列表失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('createTask')
  async createTask(@Body() createTaskDto: CreateTaskDto): Promise<Task> {
    try {
      const task = await this.taskService.createTask(createTaskDto);
      return task;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `创建任务失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('updateTaskStatus')
  async updateTaskStatus(
    @Body() updateTaskDto: UpdateTaskStatusDto,
  ): Promise<Task> {
    try {
      const task = await this.taskService.updateTaskStatus(updateTaskDto);
      return task;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `更新任务状态失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('completeTask')
  async completeTask(@Body() completeTaskDto: CompleteTaskDto): Promise<Task> {
    try {
      const task = await this.taskService.completeTask(completeTaskDto);
      return task;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `完成任务失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('deleteTaskNew')
  async deleteTask(@Body() deleteTaskDto: DeleteTaskNewDto): Promise<boolean> {
    try {
      return await this.taskService.deleteTask(deleteTaskDto.taskId);
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `删除任务失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('getTaskProgress')
  async getTaskProgress(@Query('threadId') threadId: string) {
    try {
      if (!threadId) {
        throw new Error('threadId is required');
      }
      return await this.taskService.getTaskProgress(threadId);
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `获取任务进度失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('generateTasks')
  async generateTasks(@Body() generateTasksDto: GenerateTasksDto) {
    try {
      return await this.taskService.generateTasks(generateTasksDto);
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `生成任务失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
