/**
 * 后端发送的事件类型枚举
 * 与前端 ChatEventType 保持一致
 */
export enum ChatEventType {
  MESSAGE_CHUNK = 'message_chunk',
  INTERRUPT = 'interrupt',
  ERROR = 'error',
  TASK_COMPLETE = 'task_complete',
  END = 'end',
}

/**
 * 通用事件接口
 */
export interface BaseEventData {
  timestamp?: string;
  threadId?: string;
}

/**
 * 消息块事件数据
 */
export interface MessageChunkEventData extends BaseEventData {
  content?: string;
  id?: string;
  agent?: string;
  role?: string;
  finish_reason?: string;
}

/**
 * 中断事件数据
 */
export interface InterruptEventData extends BaseEventData {
  id?: string;
  agent?: string;
  content?: string;
  role?: string;
  finish_reason?: string;
  message?: string;
  options?: Array<{ text: string; value: string }>;
  context?: Record<string, any>;
}

/**
 * 错误事件数据
 */
export interface ErrorEventData extends BaseEventData {
  error: string;
  code?: string;
  details?: any;
  recoverable?: boolean;
}

/**
 * 任务完成事件数据
 */
export interface TaskCompleteEventData extends BaseEventData {
  message: string;
  tasks?: Array<{
    id: string;
    title: string;
    type: string;
    priority: number;
  }>;
  timestamp: string;
}

/**
 * 流结束事件数据
 */
export interface EndEventData extends BaseEventData {
  reason: 'completed' | 'cancelled' | 'error' | 'timeout';
  summary?: string;
  results?: {
    totalTasks?: number;
    frontendTasks?: number;
    backendTasks?: number;
    tasks?: any[];
  };
  nextActions?: string[];
  timestamp?: string;
}

/**
 * 事件响应接口
 */
export interface ChatEventResponse {
  type: ChatEventType;
  data:
    | MessageChunkEventData
    | InterruptEventData
    | ErrorEventData
    | TaskCompleteEventData
    | EndEventData;
}
