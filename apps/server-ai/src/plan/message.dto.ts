import {
  IsString,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  IsO<PERSON>,
  IsNotEmpty,
  <PERSON>I<PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { MessageType } from './message.entity';
import { AgentType } from './graph/constants';

export class GetMessagesDto {
  @IsString()
  @IsNotEmpty()
  threadId: string;

  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  limit?: number = 50;
}

export class AddMessageDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  @IsNotEmpty()
  threadId: string;

  @IsString()
  @IsNotEmpty()
  content: string;

  @IsEnum(MessageType)
  type: MessageType;

  @IsEnum(AgentType)
  @IsOptional()
  agent?: AgentType;

  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpdateMessageDto {
  @IsString()
  @IsNotEmpty()
  messageId: string;

  @IsString()
  @IsOptional()
  content?: string;

  @IsEnum(AgentType)
  @IsOptional()
  agent?: AgentType;

  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

export class DeleteMessageDto {
  @IsString()
  @IsNotEmpty()
  messageId: string;
}
