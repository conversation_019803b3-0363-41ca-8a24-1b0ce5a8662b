import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Delete,
  HttpException,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import { ThreadService } from './thread.service';
import { Thread } from './thread.entity';
import {
  CreateThreadDto,
  UpdateThreadDto,
  GetThreadDto,
  GetThreadsDto,
  DeleteThreadDto,
  StartPlanningDto,
  SubmitPRDDto,
} from './thread.dto';
import { TransformInterceptor } from 'src/utils/transform.interceptor';

@Controller('plan')
@UseInterceptors(TransformInterceptor)
export class ThreadController {
  constructor(private readonly threadService: ThreadService) {}

  @Get('getThreads')
  async getThreads(@Query() getThreadsDto: GetThreadsDto) {
    try {
      return await this.threadService.getThreads(getThreadsDto);
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `获取会话列表失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('createThread')
  async createThread(
    @Body() createThreadDto: CreateThreadDto,
  ): Promise<Thread> {
    try {
      return await this.threadService.createThread(createThreadDto);
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `创建会话失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('getThread')
  async getThread(@Query() getThreadDto: GetThreadDto): Promise<Thread> {
    try {
      const thread = await this.threadService.getThread(getThreadDto.threadId);
      if (!thread) {
        throw new HttpException(
          {
            status: HttpStatus.NOT_FOUND,
            error: `会话 ${getThreadDto.threadId} 不存在`,
          },
          HttpStatus.NOT_FOUND,
        );
      }
      return thread;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `获取会话详情失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('updateThread')
  async updateThread(
    @Body() updateThreadDto: UpdateThreadDto & { threadId: string },
  ): Promise<Thread> {
    try {
      const { threadId, ...updateData } = updateThreadDto;
      return await this.threadService.updateThread(threadId, updateData);
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `更新会话失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('deleteThread')
  async deleteThread(
    @Body() deleteThreadDto: DeleteThreadDto,
  ): Promise<boolean> {
    try {
      await this.threadService.deleteThread(deleteThreadDto.threadId);
      return true;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `删除会话失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('startPlanning')
  async startPlanning(
    @Body() startPlanningDto: StartPlanningDto,
  ): Promise<Thread> {
    try {
      const thread = await this.threadService.startPlanning(startPlanningDto);
      return thread;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `开始规划失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('submitPRD')
  async submitPRD(@Body() submitPRDDto: SubmitPRDDto): Promise<Thread> {
    try {
      const thread = await this.threadService.submitPRD(submitPRDDto);
      return thread;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `提交PRD失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('archiveThread')
  async archiveThread(
    @Body() deleteThreadDto: DeleteThreadDto,
  ): Promise<Thread> {
    try {
      const thread = await this.threadService.archiveThread(
        deleteThreadDto.threadId,
      );
      return thread;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `归档会话失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('exportSession')
  async exportSession(@Body() deleteThreadDto: DeleteThreadDto) {
    try {
      return await this.threadService.exportSession(deleteThreadDto.threadId);
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `导出会话失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
