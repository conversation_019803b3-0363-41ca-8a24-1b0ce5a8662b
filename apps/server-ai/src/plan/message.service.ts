import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Message, MessageStatus } from './message.entity';
import { AddMessageDto, UpdateMessageDto, GetMessagesDto } from './message.dto';

@Injectable()
export class MessageService {
  constructor(
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
  ) {}

  /**
   * 获取会话消息列表
   */
  async getMessages(getMessagesDto: GetMessagesDto) {
    const { threadId, page = 1, limit = 50 } = getMessagesDto;
    const skip = (page - 1) * limit;

    const [messages, total] = await this.messageRepository.findAndCount({
      where: {
        threadId: threadId,
        status: MessageStatus.ACTIVE,
      },
      order: { created_at: 'ASC' },
      skip,
      take: limit,
    });

    return {
      items: messages,
      total,
      page,
      limit,
    };
  }

  /**
   * 添加新消息
   */
  async addMessage(addMessageDto: AddMessageDto): Promise<Message> {
    const message = new Message();
    message.id = addMessageDto.id || uuidv4();
    message.threadId = addMessageDto.threadId;
    message.type = addMessageDto.type;
    message.content = addMessageDto.content;
    message.agent = addMessageDto.agent;
    message.metadata = addMessageDto.metadata || {};
    message.status = MessageStatus.ACTIVE;

    return this.messageRepository.save(message);
  }

  /**
   * 更新消息
   */
  async updateMessage(updateMessageDto: UpdateMessageDto): Promise<Message> {
    const { messageId, ...updateData } = updateMessageDto;

    const message = await this.messageRepository.findOne({
      where: { id: messageId, status: MessageStatus.ACTIVE },
    });

    if (!message) {
      throw new Error(`Message with ID ${messageId} not found`);
    }

    // 更新字段
    if (updateData.content !== undefined) {
      message.content = updateData.content;
    }
    if (updateData.agent !== undefined) {
      message.agent = updateData.agent;
    }
    if (updateData.metadata !== undefined) {
      message.metadata = { ...message.metadata, ...updateData.metadata };
    }

    return this.messageRepository.save(message);
  }

  /**
   * 删除消息（软删除）
   */
  async deleteMessage(messageId: string): Promise<boolean> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId, status: MessageStatus.ACTIVE },
    });

    if (!message) {
      throw new Error(`Message with ID ${messageId} not found`);
    }

    message.status = MessageStatus.DELETED;
    await this.messageRepository.save(message);

    return true;
  }

  /**
   * 根据线程ID获取最新的几条消息
   */
  async getRecentMessages(
    threadId: string,
    limit: number = 10,
  ): Promise<Message[]> {
    return this.messageRepository.find({
      where: {
        threadId: threadId,
        status: MessageStatus.ACTIVE,
      },
      order: { created_at: 'DESC' },
      take: limit,
    });
  }
}
