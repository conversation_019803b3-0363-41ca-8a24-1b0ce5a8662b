import {
  IsString,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  IsO<PERSON>,
  IsNotEmpty,
  IsInt,
  IsArray,
  Min,
  Max,
} from 'class-validator';
import { TaskType, TaskStatus } from './task.entity';

export class GetTasksDto {
  @IsString()
  @IsNotEmpty()
  threadId: string;

  @IsEnum(TaskStatus)
  @IsOptional()
  status?: TaskStatus;
}

export class CreateTaskDto {
  @IsString()
  @IsNotEmpty()
  threadId: string;

  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsEnum(TaskType)
  type: TaskType;

  @IsArray()
  @IsOptional()
  dependencies?: string[];

  @IsInt()
  @Min(0)
  @Max(10)
  @IsOptional()
  priority?: number = 0;
}

export class UpdateTaskStatusDto {
  @IsString()
  @IsNotEmpty()
  taskId: string;

  @IsEnum(TaskStatus)
  @IsOptional()
  status?: TaskStatus;

  @IsInt()
  @Min(0)
  @Max(100)
  @IsOptional()
  progress?: number;
}

export class CompleteTaskDto {
  @IsString()
  @IsNotEmpty()
  taskId: string;

  @IsString()
  @IsOptional()
  completionNotes?: string;

  @IsString()
  @IsOptional()
  deploymentUrl?: string;
}

export class DeleteTaskNewDto {
  @IsString()
  @IsNotEmpty()
  taskId: string;
}

export class GenerateTasksDto {
  @IsString()
  @IsNotEmpty()
  threadId: string;

  @IsOptional()
  forceRegenerate?: boolean = false;
}
