# Plan API 接口文档

## 概述

Plan模块提供了会话（Thread）、消息（Message）、任务（Task）管理以及AI对话的完整API接口。所有接口均采用语义化设计，统一使用GET/POST方法。

## Thread API - 会话管理

### 1. 获取会话列表
```
GET /api/plan/getThreads?page=1&limit=10&status=1&userId=xxx
```
**功能**: 获取会话列表，支持分页和筛选  
**参数**:
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认10，最大100
- `status` (可选): 会话状态筛选 (0:已删除, 1:处理中, 2:已完成)
- `userId` (可选): 用户ID筛选

**响应**:
```json
{
  "success": true,
  "data": {
    "threads": [...],
    "total": 50,
    "page": 1,
    "limit": 10
  }
}
```

### 2. 创建新会话
```
POST /api/plan/createThread
```
**功能**: 创建新的会话  
**请求体**:
```json
{
  "title": "会话标题",
  "user_id": "用户ID",
  "prdLink": "PRD链接(可选)",
  "metadata": {},
  "messages": [],
  "state": {},
  "tasks": []
}
```

### 3. 获取会话详情
```
GET /api/plan/getThread?threadId=<threadId>
```
**功能**: 获取指定会话的详细信息

### 4. 更新会话信息
```
POST /api/plan/updateThread
```
**功能**: 更新会话的基本信息  
**请求体**:
```json
{
  "threadId": "会话ID",
  "title": "新标题(可选)",
  "status": 1,
  "metadata": {},
  "messages": [],
  "state": {},
  "tasks": []
}
```

### 5. 删除会话
```
POST /api/plan/deleteThread
```
**功能**: 软删除会话  
**请求体**:
```json
{
  "threadId": "会话ID"
}
```

### 6. 开始规划流程
```
POST /api/plan/startPlanning
```
**功能**: 开始新项目规划，创建会话并提交PRD  
**请求体**:
```json
{
  "userId": "用户ID",
  "prdContent": "PRD内容",
  "projectName": "项目名称(可选)"
}
```

### 7. 提交PRD内容
```
POST /api/plan/submitPRD
```
**功能**: 向现有会话提交或更新PRD内容  
**请求体**:
```json
{
  "threadId": "会话ID",
  "prdContent": "PRD内容",
  "additionalInfo": {}
}
```

### 8. 归档会话
```
POST /api/plan/archiveThread
```
**功能**: 将会话标记为已完成状态  
**请求体**:
```json
{
  "threadId": "会话ID"
}
```

### 9. 导出会话数据
```
POST /api/plan/exportSession
```
**功能**: 导出会话的完整数据，包括消息历史  
**请求体**:
```json
{
  "threadId": "会话ID"
}
```

## Message API - 消息管理

### 1. 获取消息列表
```
GET /api/plan/getMessages?threadId=<threadId>&page=1&limit=50
```
**功能**: 获取指定会话的消息列表  
**参数**:
- `threadId`: 会话ID
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认50，最大100

### 2. 添加消息
```
POST /api/plan/addMessage
```
**功能**: 向会话添加新消息  
**请求体**:
```json
{
  "threadId": "会话ID",
  "content": "消息内容",
  "type": "user|assistant|system|interrupt|tools",
  "metadata": {}
}
```

### 3. 更新消息
```
POST /api/plan/updateMessage
```
**功能**: 更新现有消息  
**请求体**:
```json
{
  "messageId": "消息ID",
  "content": "新内容(可选)",
  "metadata": {}
}
```

### 4. 删除消息
```
POST /api/plan/deleteMessage
```
**功能**: 软删除消息  
**请求体**:
```json
{
  "messageId": "消息ID"
}
```

## Task API - 任务管理

### 1. 获取任务列表
```
GET /api/plan/getTasks?threadId=<threadId>&status=pending
```
**功能**: 获取指定会话的任务列表  
**参数**:
- `threadId`: 会话ID
- `status` (可选): 任务状态筛选

**响应**:
```json
{
  "success": true,
  "data": {
    "tasks": [...],
    "summary": {
      "total": 5,
      "completed": 2,
      "pending": 2,
      "inProgress": 1
    }
  }
}
```

### 2. 创建任务
```
POST /api/plan/createTask
```
**功能**: 创建新任务  
**请求体**:
```json
{
  "threadId": "会话ID",
  "title": "任务标题",
  "description": "任务描述",
  "type": "frontend|backend|integration",
  "dependencies": ["依赖任务ID"],
  "priority": 5,
  "metadata": {}
}
```

### 3. 更新任务状态
```
POST /api/plan/updateTaskStatus
```
**功能**: 更新任务状态和进度  
**请求体**:
```json
{
  "taskId": "任务ID",
  "status": "pending|in_progress|completed",
  "progress": 50,
  "metadata": {}
}
```

### 4. 完成任务
```
POST /api/plan/completeTask
```
**功能**: 标记任务为完成状态  
**请求体**:
```json
{
  "taskId": "任务ID",
  "completionNotes": "完成说明",
  "deploymentUrl": "部署链接"
}
```

### 5. 删除任务
```
POST /api/plan/deleteTaskNew
```
**功能**: 软删除任务  
**请求体**:
```json
{
  "taskId": "任务ID"
}
```

### 6. 获取任务进度
```
GET /api/plan/getTaskProgress?threadId=<threadId>
```
**功能**: 获取会话的任务进度统计  

**响应**:
```json
{
  "success": true,
  "data": {
    "overall": {
      "total": 5,
      "completed": 2,
      "percentage": 40
    },
    "byType": {
      "frontend": { "total": 2, "completed": 1 },
      "backend": { "total": 2, "completed": 1 },
      "integration": { "total": 1, "completed": 0 }
    }
  }
}
```

### 7. 生成任务列表
```
POST /api/plan/generateTasks
```
**功能**: AI自动生成任务列表  
**请求体**:
```json
{
  "threadId": "会话ID",
  "forceRegenerate": false
}
```

## Chat API - AI对话

### 1. 流式对话
```
POST /api/plan/stream
```
**功能**: 与AI进行流式对话  
**请求体**:
```json
{
  "messages": [
    {
      "role": "user",
      "content": "用户消息"
    }
  ],
  "threadId": "会话ID或__default__"
}
```

### 2. 重新生成回复
```
POST /api/plan/stream/regenerate
```
**功能**: 重新生成AI回复  
**请求体**:
```json
{
  "threadId": "会话ID",
  "messageIndex": 3
}
```

## 数据模型

### Thread (会话)
```typescript
{
  id: string;
  user_id: string;
  title: string;
  prdLink: string;
  created_at: Date;
  updated_at: Date;
  metadata: Record<string, any>;
  status: 0|1|2; // 0:已删除, 1:处理中, 2:已完成
  messages: any[]; // 兼容字段
  state: Record<string, any>;
  tasks: any[]; // 兼容字段
}
```

### Message (消息)
```typescript
{
  id: string;
  threadId: string;
  type: 'user'|'assistant'|'system'|'interrupt'|'tools';
  content: string;
  status: 0|1; // 0:已删除, 1:正常
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}
```

### Task (任务)
```typescript
{
  id: string;
  threadId: string;
  title: string;
  description: string;
  type: 'frontend'|'backend'|'integration';
  status: 'pending'|'in_progress'|'completed'|'deleted';
  priority: number; // 0-10
  progress: number; // 0-100
  dependencies: string[];
  metadata: Record<string, any>;
  completion_notes: string;
  deployment_url: string;
  created_at: Date;
  updated_at: Date;
}
```

## 错误处理

所有API都采用统一的错误响应格式：

```json
{
  "status": 500,
  "error": "错误描述信息"
}
```

常见HTTP状态码：
- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用示例

### 完整的项目规划流程

1. **开始规划**
```bash
curl -X POST /api/plan/startPlanning \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "prdContent": "构建一个任务管理应用...",
    "projectName": "TaskManager"
  }'
```

2. **生成任务**
```bash
curl -X POST /api/plan/generateTasks \
  -H "Content-Type: application/json" \
  -d '{
    "threadId": "thread-uuid"
  }'
```

3. **查看任务进度**
```bash
curl "/api/plan/getTaskProgress?threadId=thread-uuid"
```

4. **AI对话优化**
```bash
curl -X POST /api/plan/stream \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "请优化前端任务"}],
    "threadId": "thread-uuid"
  }'
```

这套API提供了完整的项目规划、任务管理和AI交互能力，支持前端构建现代化的用户界面。 
