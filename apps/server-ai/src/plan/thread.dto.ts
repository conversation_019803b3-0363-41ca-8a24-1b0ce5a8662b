import {
  IsString,
  <PERSON>Optional,
  <PERSON><PERSON><PERSON>,
  IsArray,
  IsObject,
  IsNotEmpty,
  IsInt,
  Min,
  Max,
  IsDate,
} from 'class-validator';
import { ThreadStatus, InterruptStatus } from './thread.entity';

export class CreateThreadDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsOptional()
  user_id?: string;

  @IsString()
  @IsOptional()
  prdLink?: string;

  @IsObject()
  @IsOptional()
  state?: Record<string, any>;
}

export class UpdateThreadDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  prdLink?: string;

  @IsEnum(ThreadStatus)
  @IsOptional()
  status?: ThreadStatus;

  @IsObject()
  @IsOptional()
  state?: Record<string, any>;

  @IsEnum(InterruptStatus)
  @IsOptional()
  interrupt_status?: InterruptStatus;

  @IsObject()
  @IsOptional()
  interrupt_data?: Record<string, any>;

  @IsDate()
  @IsOptional()
  interrupt_at?: Date;
}

export class GetThreadDto {
  @IsString()
  @IsNotEmpty()
  threadId: string;
}

export class GetThreadsDto {
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  limit?: number = 10;

  @IsEnum(ThreadStatus)
  @IsOptional()
  status?: ThreadStatus;

  @IsString()
  @IsOptional()
  userId?: string;
}

export class DeleteThreadDto {
  @IsString()
  @IsNotEmpty()
  threadId: string;
}

// 新增业务流程相关DTO
export class StartPlanningDto {
  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  prdContent: string;

  @IsString()
  @IsOptional()
  projectName?: string;
}

export class SubmitPRDDto {
  @IsString()
  @IsNotEmpty()
  threadId: string;

  @IsString()
  @IsNotEmpty()
  prdContent: string;

  @IsObject()
  @IsOptional()
  additionalInfo?: Record<string, any>;
}
