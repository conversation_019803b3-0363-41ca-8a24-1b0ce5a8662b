import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Thread, ThreadStatus, InterruptStatus } from './thread.entity';
import { Message, MessageType } from './message.entity';
import {
  CreateThreadDto,
  UpdateThreadDto,
  StartPlanningDto,
  SubmitPRDDto,
} from './thread.dto';

@Injectable()
export class ThreadService {
  constructor(
    @InjectRepository(Thread)
    private threadRepository: Repository<Thread>,
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
  ) {}

  /**
   * 获取会话列表（支持分页和过滤）
   */
  async getThreads(getThreadsDto?: any) {
    const { page = 1, limit = 10, status, userId } = getThreadsDto || {};
    const skip = (page - 1) * limit;

    // 限制单次查询的最大数量，防止大量数据排序
    const maxLimit = Math.min(limit, 50);

    const where: any = {};

    // 如果指定了状态，使用指定状态，否则排除已删除的
    if (status !== undefined) {
      where.status = status;
    } else {
      where.status = ThreadStatus.PROCESSING; // 默认只显示处理中的
    }

    // 如果指定了用户ID
    if (userId) {
      where.user_id = userId;
    }

    try {
      // 使用关联查询，同时获取 tasks 和 messages
      const queryBuilder = this.threadRepository
        .createQueryBuilder('thread')
        .leftJoinAndSelect(
          'thread.tasks',
          'task',
          'task.status != :deletedStatus',
          { deletedStatus: 'deleted' },
        )
        .leftJoinAndSelect('thread.messages', 'message')
        .select([
          'thread.id',
          'thread.user_id',
          'thread.title',
          'thread.prdLink',
          'thread.status',
          'thread.created_at',
          'thread.updated_at',
          // Task 字段
          'task.id',
          'task.title',
          'task.description',
          'task.type',
          'task.status',
          'task.priority',
          'task.progress',
          'task.created_at',
          'task.updated_at',
          // Message 字段 (限制查询，避免数据过大)
          'message.id',
          'message.type',
          'message.content',
          'message.agent',
          'message.created_at',
        ])
        .where(where)
        .orderBy('thread.updated_at', 'DESC')
        .addOrderBy('task.priority', 'DESC')
        .addOrderBy('task.created_at', 'ASC')
        .addOrderBy('message.created_at', 'ASC')
        .skip(skip)
        .take(maxLimit);

      // 获取数据
      const threads = await queryBuilder.getMany();

      // 单独计算总数（不包含排序，性能更好）
      const total = await this.threadRepository.count({ where });

      return {
        items: threads,
        total,
        page,
        limit: maxLimit,
      };
    } catch (error) {
      console.error('获取会话列表失败:', error);

      // 如果关联查询失败，回退到基础查询
      const [threads, total] = await this.threadRepository.findAndCount({
        where,
        relations: ['tasks', 'messages'],
        skip,
        take: maxLimit,
        order: {
          updated_at: 'DESC',
        },
      });

      return {
        items: threads,
        total,
        page,
        limit: maxLimit,
        warning: '使用了回退查询方案',
      };
    }
  }

  /**
   * 创建新会话
   */
  async createThread(
    createThreadDto: CreateThreadDto & { id?: string },
  ): Promise<Thread> {
    const thread = new Thread();
    thread.id = createThreadDto.id || uuidv4(); // 支持预设ID
    thread.title = createThreadDto.title;
    thread.user_id = createThreadDto.user_id;
    thread.prdLink = createThreadDto.prdLink;
    thread.state = createThreadDto.state || {};
    thread.status = ThreadStatus.PROCESSING;

    return this.threadRepository.save(thread);
  }

  /**
   * 获取会话详情
   */
  async getThread(threadId: string): Promise<Thread> {
    return this.threadRepository.findOne({
      where: { id: threadId },
      relations: ['tasks', 'messages'],
    });
  }

  /**
   * 更新会话信息
   */
  async updateThread(
    threadId: string,
    updateThreadDto: UpdateThreadDto,
  ): Promise<Thread> {
    const thread = await this.threadRepository.findOne({
      where: { id: threadId },
    });
    if (!thread) {
      throw new Error(`Thread with ID ${threadId} not found`);
    }

    // 更新字段
    if (updateThreadDto.title !== undefined)
      thread.title = updateThreadDto.title;
    if (updateThreadDto.prdLink !== undefined)
      thread.prdLink = updateThreadDto.prdLink;
    if (updateThreadDto.status !== undefined)
      thread.status = updateThreadDto.status;
    if (updateThreadDto.state !== undefined)
      thread.state = updateThreadDto.state;

    // 新增：更新中断状态字段
    if (updateThreadDto.interrupt_status !== undefined)
      thread.interrupt_status = updateThreadDto.interrupt_status;
    if (updateThreadDto.interrupt_data !== undefined)
      thread.interrupt_data = updateThreadDto.interrupt_data;
    if (updateThreadDto.interrupt_at !== undefined)
      thread.interrupt_at = updateThreadDto.interrupt_at;

    return this.threadRepository.save(thread);
  }

  /**
   * 新增：设置中断状态
   */
  async setInterruptStatus(
    threadId: string,
    interruptStatus: InterruptStatus,
    interruptData?: Record<string, any>,
  ): Promise<Thread> {
    const thread = await this.threadRepository.findOne({
      where: { id: threadId },
    });
    if (!thread) {
      throw new Error(`Thread with ID ${threadId} not found`);
    }

    thread.interrupt_status = interruptStatus;
    thread.interrupt_data = interruptData || null;
    thread.interrupt_at =
      interruptStatus === InterruptStatus.WAITING_FEEDBACK ? new Date() : null;

    return this.threadRepository.save(thread);
  }

  /**
   * 新增：清除中断状态
   */
  async clearInterruptStatus(threadId: string): Promise<Thread> {
    return this.setInterruptStatus(threadId, InterruptStatus.NONE, null);
  }

  /**
   * 新增：检查是否有待处理的中断
   */
  async hasWaitingInterrupt(threadId: string): Promise<boolean> {
    const thread = await this.threadRepository.findOne({
      where: { id: threadId },
      select: ['interrupt_status'],
    });
    return thread?.interrupt_status === InterruptStatus.WAITING_FEEDBACK;
  }

  /**
   * 新增：获取中断信息
   */
  async getInterruptInfo(threadId: string): Promise<{
    status: InterruptStatus;
    data: Record<string, any> | null;
    interruptAt: Date | null;
  } | null> {
    const thread = await this.threadRepository.findOne({
      where: { id: threadId },
      select: ['interrupt_status', 'interrupt_data', 'interrupt_at'],
    });

    if (!thread) {
      return null;
    }

    return {
      status: thread.interrupt_status,
      data: thread.interrupt_data,
      interruptAt: thread.interrupt_at,
    };
  }

  /**
   * 删除会话（软删除）
   */
  async deleteThread(threadId: string): Promise<void> {
    const thread = await this.threadRepository.findOne({
      where: { id: threadId },
    });
    if (!thread) {
      throw new Error(`Thread with ID ${threadId} not found`);
    }

    // 软删除，将状态设置为已删除
    thread.status = ThreadStatus.DELETED;
    await this.threadRepository.save(thread);
  }

  /**
   * 开始规划流程
   */
  async startPlanning(startPlanningDto: StartPlanningDto): Promise<Thread> {
    const { userId, prdContent, projectName } = startPlanningDto;

    // 创建新会话
    const thread = new Thread();
    thread.id = uuidv4();
    thread.title = projectName || '新项目规划';
    thread.user_id = userId;

    thread.state = {
      currentStep: 'prd_analysis',
      prdSubmitted: true,
    };
    thread.status = ThreadStatus.PROCESSING;

    const savedThread = await this.threadRepository.save(thread);

    // 创建初始消息
    const initialMessage = new Message();
    initialMessage.id = uuidv4();
    initialMessage.threadId = savedThread.id;
    initialMessage.type = MessageType.SYSTEM;
    initialMessage.content = `开始新项目规划，PRD内容已提交：\n\n${prdContent}`;

    await this.messageRepository.save(initialMessage);

    return savedThread;
  }

  /**
   * 提交PRD内容
   */
  async submitPRD(submitPRDDto: SubmitPRDDto): Promise<Thread> {
    const { threadId, prdContent, additionalInfo } = submitPRDDto;

    const thread = await this.threadRepository.findOne({
      where: { id: threadId },
    });
    if (!thread) {
      throw new Error(`Thread with ID ${threadId} not found`);
    }

    thread.state = {
      ...thread.state,
      currentStep: 'prd_updated',
      prdSubmitted: true,
    };

    const savedThread = await this.threadRepository.save(thread);

    // 创建PRD更新消息
    const prdMessage = new Message();
    prdMessage.id = uuidv4();
    prdMessage.threadId = threadId;
    prdMessage.type = MessageType.USER;
    prdMessage.content = `PRD内容已更新：\n\n${prdContent}`;

    await this.messageRepository.save(prdMessage);

    return savedThread;
  }

  /**
   * 归档会话
   */
  async archiveThread(threadId: string): Promise<Thread> {
    const thread = await this.threadRepository.findOne({
      where: { id: threadId },
    });
    if (!thread) {
      throw new Error(`Thread with ID ${threadId} not found`);
    }

    // 设置为已完成状态
    thread.status = ThreadStatus.COMPLETED;

    return this.threadRepository.save(thread);
  }

  /**
   * 导出会话数据
   */
  async exportSession(threadId: string) {
    const thread = await this.threadRepository.findOne({
      where: { id: threadId },
    });
    if (!thread) {
      throw new Error(`Thread with ID ${threadId} not found`);
    }

    // 获取相关消息
    const messages = await this.messageRepository.find({
      where: { threadId: threadId },
      order: { created_at: 'ASC' },
    });

    return {
      thread: {
        id: thread.id,
        title: thread.title,
        status: thread.status,
        created_at: thread.created_at,
        updated_at: thread.updated_at,
        state: thread.state,
        tasks: thread.tasks,
      },
      messages: messages.map((msg) => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        created_at: msg.created_at,
      })),
      exportedAt: new Date().toISOString(),
    };
  }
}
