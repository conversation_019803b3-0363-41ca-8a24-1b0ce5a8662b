import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Chat<PERSON>ontroller } from './chat.controller';
import { ThreadController } from './thread.controller';
import { MessageController } from './message.controller';
import { TaskController } from './task.controller';
import { ThreadService } from './thread.service';
import { MessageService } from './message.service';
import { TaskService } from './task.service';
import { Thread } from './thread.entity';
import { Message } from './message.entity';
import { Task } from './task.entity';

@Module({
  imports: [
    // 注册实体
    TypeOrmModule.forFeature([Thread, Message, Task]),
  ],
  controllers: [
    // API控制器
    ChatController,
    ThreadController,
    MessageController,
    TaskController,
  ],
  providers: [
    // 服务
    ThreadService,
    MessageService,
    TaskService,
  ],
})
export class PlanModule {}
