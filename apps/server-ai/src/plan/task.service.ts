import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Task, TaskStatus, TaskType } from './task.entity';
import {
  CreateTaskDto,
  UpdateTaskStatusDto,
  CompleteTaskDto,
  GetTasksDto,
  GenerateTasksDto,
} from './task.dto';

@Injectable()
export class TaskService {
  constructor(
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
  ) {}

  /**
   * 获取会话任务列表
   */
  async getTasks(getTasksDto: GetTasksDto) {
    const { threadId, status } = getTasksDto;

    const where: any = {
      threadId: threadId,
      status: status ? status : Not(TaskStatus.DELETED),
    };

    const tasks = await this.taskRepository.find({
      where,
      order: { priority: 'DESC', created_at: 'ASC' },
    });

    // 计算统计信息
    const total = tasks.length;
    const completed = tasks.filter(
      (task) => task.status === TaskStatus.COMPLETED,
    ).length;
    const pending = tasks.filter(
      (task) => task.status === TaskStatus.PENDING,
    ).length;
    const inProgress = tasks.filter(
      (task) => task.status === TaskStatus.IN_PROGRESS,
    ).length;

    return {
      items: tasks,
      summary: {
        total,
        completed,
        pending,
        inProgress,
      },
    };
  }

  /**
   * 创建新任务
   */
  async createTask(createTaskDto: CreateTaskDto): Promise<Task> {
    const task = new Task();
    task.id = uuidv4();
    task.threadId = createTaskDto.threadId;
    task.title = createTaskDto.title;
    task.description = createTaskDto.description || '';
    task.type = createTaskDto.type;
    task.dependencies = createTaskDto.dependencies || [];
    task.priority = createTaskDto.priority || 0;
    task.status = TaskStatus.PENDING;
    task.progress = 0;

    return this.taskRepository.save(task);
  }

  /**
   * 从AI生成的JSON批量保存任务
   */
  async saveTasksFromJSON(
    threadId: string,
    tasksJSON: string,
  ): Promise<Task[]> {
    try {
      const tasks = JSON.parse(tasksJSON);
      const savedTasks: Task[] = [];

      // 先删除该线程的现有任务（硬删除，避免主键冲突）
      await this.taskRepository.delete({ threadId });

      for (const taskData of tasks) {
        const task = new Task();
        // 总是生成新的UUID，不使用taskData.id，避免主键重复
        task.id = uuidv4();
        task.threadId = threadId;
        task.title = taskData.title;
        task.description = taskData.description || '';

        // 映射任务类型
        if (taskData.type === 'frontend' || taskData.type === 'Frontend') {
          task.type = TaskType.FRONTEND;
        } else if (taskData.type === 'backend' || taskData.type === 'Backend') {
          task.type = TaskType.BACKEND;
        } else {
          task.type = TaskType.FRONTEND; // 默认
        }

        task.dependencies = taskData.dependencies || [];
        task.priority = this.mapPriority(taskData.priority);
        task.status = TaskStatus.PENDING;
        task.progress = 0;

        const savedTask = await this.taskRepository.save(task);
        savedTasks.push(savedTask);
      }

      return savedTasks;
    } catch (error) {
      console.error('[TaskService] 保存任务失败:', error);
      throw new Error(`保存任务失败: ${error.message}`);
    }
  }

  /**
   * 映射优先级
   */
  private mapPriority(priority: any): number {
    if (typeof priority === 'number') return priority;
    if (priority === 'high') return 3;
    if (priority === 'medium') return 2;
    if (priority === 'low') return 1;
    return 1; // 默认
  }

  /**
   * 更新任务状态
   */
  async updateTaskStatus(updateTaskDto: UpdateTaskStatusDto): Promise<Task> {
    const { taskId, ...updateData } = updateTaskDto;

    const task = await this.taskRepository.findOne({
      where: { id: taskId, status: Not(TaskStatus.DELETED) },
    });

    if (!task) {
      throw new Error(`Task with ID ${taskId} not found`);
    }

    // 更新字段
    if (updateData.status !== undefined) {
      task.status = updateData.status;
    }
    if (updateData.progress !== undefined) {
      task.progress = updateData.progress;
    }

    return this.taskRepository.save(task);
  }

  /**
   * 完成任务
   */
  async completeTask(completeTaskDto: CompleteTaskDto): Promise<Task> {
    const { taskId, completionNotes, deploymentUrl } = completeTaskDto;

    const task = await this.taskRepository.findOne({
      where: { id: taskId, status: Not(TaskStatus.DELETED) },
    });

    if (!task) {
      throw new Error(`Task with ID ${taskId} not found`);
    }

    task.status = TaskStatus.COMPLETED;
    task.progress = 100;
    if (completionNotes) {
      task.completion_notes = completionNotes;
    }
    if (deploymentUrl) {
      task.deployment_url = deploymentUrl;
    }

    return this.taskRepository.save(task);
  }

  /**
   * 删除任务（软删除）
   */
  async deleteTask(taskId: string): Promise<boolean> {
    const task = await this.taskRepository.findOne({
      where: { id: taskId, status: Not(TaskStatus.DELETED) },
    });

    if (!task) {
      throw new Error(`Task with ID ${taskId} not found`);
    }

    task.status = TaskStatus.DELETED;
    await this.taskRepository.save(task);

    return true;
  }

  /**
   * 获取任务进度统计
   */
  async getTaskProgress(threadId: string) {
    const tasks = await this.taskRepository.find({
      where: {
        threadId: threadId,
        status: Not(TaskStatus.DELETED),
      },
    });

    const total = tasks.length;
    const completed = tasks.filter(
      (task) => task.status === TaskStatus.COMPLETED,
    ).length;

    // 按类型统计
    const byType = {
      frontend: {
        total: tasks.filter((task) => task.type === TaskType.FRONTEND).length,
        completed: tasks.filter(
          (task) =>
            task.type === TaskType.FRONTEND &&
            task.status === TaskStatus.COMPLETED,
        ).length,
      },
      backend: {
        total: tasks.filter((task) => task.type === TaskType.BACKEND).length,
        completed: tasks.filter(
          (task) =>
            task.type === TaskType.BACKEND &&
            task.status === TaskStatus.COMPLETED,
        ).length,
      },
      integration: {
        total: tasks.filter((task) => task.type === TaskType.INTEGRATION)
          .length,
        completed: tasks.filter(
          (task) =>
            task.type === TaskType.INTEGRATION &&
            task.status === TaskStatus.COMPLETED,
        ).length,
      },
    };

    return {
      overall: {
        total,
        completed,
        percentage: total > 0 ? Math.round((completed / total) * 100) : 0,
      },
      byType,
    };
  }

  /**
   * 生成任务列表（AI生成逻辑，后续集成LangGraph）
   */
  async generateTasks(generateTasksDto: GenerateTasksDto) {
    const { threadId, forceRegenerate } = generateTasksDto;

    // 检查是否已有任务
    if (!forceRegenerate) {
      const existingTasks = await this.taskRepository.find({
        where: {
          threadId: threadId,
          status: Not(TaskStatus.DELETED),
        },
      });

      if (existingTasks.length > 0) {
        return {
          message: '任务已存在，如需重新生成请设置 forceRegenerate 为 true',
          existingTasks,
        };
      }
    }

    // TODO: 集成LangGraph进行AI任务生成
    // 这里先创建一些示例任务
    const sampleTasks = [
      {
        title: '前端页面开发',
        description: '实现用户界面和交互逻辑',
        type: TaskType.FRONTEND,
        priority: 5,
      },
      {
        title: '后端API开发',
        description: '实现业务逻辑和数据接口',
        type: TaskType.BACKEND,
        priority: 8,
      },
      {
        title: '前后端集成测试',
        description: '联调和端到端测试',
        type: TaskType.INTEGRATION,
        priority: 3,
        dependencies: [], // 将在实际任务创建后填入ID
      },
    ];

    const createdTasks = [];
    for (const taskData of sampleTasks) {
      const createDto: CreateTaskDto = {
        threadId,
        title: taskData.title,
        description: taskData.description,
        type: taskData.type,
        priority: taskData.priority,
        dependencies: taskData.dependencies,
      };

      const task = await this.createTask(createDto);
      createdTasks.push(task);
    }

    return {
      message: '任务生成成功',
      tasks: createdTasks,
    };
  }
}
