import {
  <PERSON><PERSON><PERSON>,
  Column,
  <PERSON>C<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Thread } from './thread.entity';
import { AgentType } from './graph/constants';

export enum MessageType {
  USER = 'user',
  ASSISTANT = 'assistant',
  SYSTEM = 'system',
  INTERRUPT = 'interrupt',
  TOOLS = 'tools',
}

export enum MessageStatus {
  ACTIVE = 1,
  DELETED = 0,
}

@Entity('messages')
export class Message {
  @PrimaryColumn()
  id: string;

  @Column()
  threadId: string;

  @Column({
    type: 'enum',
    enum: MessageType,
    default: MessageType.USER,
  })
  type: MessageType;

  @Column({ type: 'text' })
  content: string;

  @Column({
    type: 'enum',
    enum: AgentType,
    nullable: true,
  })
  agent: AgentType;

  @Column({
    type: 'int',
    default: MessageStatus.ACTIVE,
  })
  status: MessageStatus;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => Thread, (thread) => thread.messages)
  @JoinColumn({ name: 'threadId' })
  thread: Thread;
}
