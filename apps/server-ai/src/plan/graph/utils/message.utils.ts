import { ImageInfo } from '../types/state.types';

/**
 * 创建包含图片的消息内容
 * @param textContent 文本内容
 * @param images 图片信息数组
 * @returns 格式化的消息内容数组
 */
export function createMessageWithImages(
  textContent: string,
  images: ImageInfo[],
): any[] {
  const content: any[] = [
    {
      type: 'text',
      text: textContent,
    },
  ];

  // 添加成功处理的图片
  images.forEach((image, index) => {
    if (image.base64 && !image.error) {
      content.push({
        type: 'image_url',
        image_url: {
          url: image.base64,
          detail: 'high', // 使用高质量图片分析
        },
      });
    } else if (image.error) {
      console.warn(
        `跳过有错误的图片${index + 1}: ${image.url}, 错误: ${image.error}`,
      );
    }
  });

  return content;
}

/**
 * 验证图片信息
 * @param image 图片信息
 * @returns 验证结果
 */
export function validateImageInfo(image: ImageInfo): boolean {
  try {
    if (!image.url || image.url.length === 0) {
      return false;
    }

    if (!image.base64 || image.base64.length === 0) {
      return false;
    }

    // 检查base64格式
    if (!image.base64.startsWith('data:image/')) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('图片信息验证失败:', error);
    return false;
  }
}

/**
 * 过滤有效的图片
 * @param images 图片信息数组
 * @returns 有效的图片数组
 */
export function filterValidImages(images: ImageInfo[]): ImageInfo[] {
  return images.filter((image) => validateImageInfo(image));
}

/**
 * 创建图片摘要信息
 * @param images 图片信息数组
 * @returns 图片摘要字符串
 */
export function createImageSummary(images: ImageInfo[]): string {
  if (!images || images.length === 0) {
    return '无图片内容';
  }

  const validImages = filterValidImages(images);
  const errorImages = images.filter((img) => img.error);

  let summary = `共${images.length}张图片`;

  if (validImages.length > 0) {
    summary += `，其中${validImages.length}张处理成功`;
  }

  if (errorImages.length > 0) {
    summary += `，${errorImages.length}张处理失败`;
  }

  return summary;
}

/**
 * 格式化消息内容为字符串
 * @param content 消息内容
 * @returns 格式化的字符串
 */
export function formatMessageContent(content: any): string {
  if (typeof content === 'string') {
    return content;
  }

  if (Array.isArray(content)) {
    return content
      .filter((item) => item.type === 'text')
      .map((item) => item.text)
      .join('\n');
  }

  if (content && typeof content === 'object' && content.toString) {
    return content.toString();
  }

  return JSON.stringify(content);
}

/**
 * 创建错误消息
 * @param error 错误对象或字符串
 * @param context 上下文信息
 * @returns 格式化的错误消息
 */
export function createErrorMessage(error: any, context?: string): string {
  let errorMessage = '处理过程中发生错误';

  if (typeof error === 'string') {
    errorMessage = error;
  } else if (error && error.message) {
    errorMessage = error.message;
  }

  if (context) {
    errorMessage = `${context}: ${errorMessage}`;
  }

  return errorMessage;
}

/**
 * 截断长文本
 * @param text 原始文本
 * @param maxLength 最大长度
 * @param suffix 截断后缀
 * @returns 截断后的文本
 */
export function truncateText(
  text: string,
  maxLength: number = 1000,
  suffix: string = '...',
): string {
  if (!text || text.length <= maxLength) {
    return text;
  }

  return text.substring(0, maxLength - suffix.length) + suffix;
}
