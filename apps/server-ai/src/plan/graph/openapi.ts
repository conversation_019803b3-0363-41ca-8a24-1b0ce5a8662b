import axios, { type AxiosRequestConfig } from 'axios';
import * as fs from 'node:fs/promises';
import * as path from 'node:path';

interface Options {
  appKey: string;
  secretKey?: string;
  host: string;
  raw?: boolean;
}

interface CacheEntry {
  accessToken: string;
  expireTime: number;
  createdAt: number;
}

// 简单的内存缓存
class MemoryCache {
  private cache = new Map<string, CacheEntry>();

  get(key: string): string | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // 检查是否过期
    const now = Date.now();
    if (now - entry.createdAt > entry.expireTime * 1000) {
      this.cache.delete(key);
      return null;
    }

    return entry.accessToken;
  }

  set(key: string, accessToken: string, expireTime: number): void {
    this.cache.set(key, {
      accessToken,
      expireTime,
      createdAt: Date.now(),
    });
  }

  delete(key: string): void {
    this.cache.delete(key);
  }
}

export class OpenApiClient {
  private cache = new MemoryCache();

  async request<T>({
    url,
    method,
    body,
    headers,
    options,
    responseType,
  }: {
    url: string;
    method: string;
    body?: Record<string, any>;
    headers?: Record<string, string | number>;
    options: Options;
    responseType?: 'json' | 'arraybuffer' | 'blob' | 'text' | 'stream';
  }): Promise<T> {
    const { accessToken, delHandler } =
      await this.getAccessTokenWithCache(options);

    const _url = `${options.host}${url}`;

    try {
      const response = await axios({
        url: _url,
        method,
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          ...headers,
        },
        data: body,
        responseType: responseType || 'json',
      } as AxiosRequestConfig);

      if (response.status === 200) {
        return response.data;
      }

      // 进入兜底 catch 处理, 删除 accessToken 上游重新调用
      throw response;
    } catch (error) {
      await delHandler();
      throw error;
    }
  }

  /**
   * 添加缓存
   */
  private async getAccessTokenWithCache(options: Options): Promise<{
    accessToken: string;
    delHandler: () => Promise<void>;
  }> {
    const key = `OPENAPI_ACCESSTOKEN::${options.host}_${options.appKey}`;

    const accessToken = this.cache.get(key);

    const delHandler = async () => {
      this.cache.delete(key);
      console.info(`[open-api-client] delete accessToken succeeded`);
    };

    if (accessToken) {
      console.info(
        `[open-api-client] get accessToken from cache accessToken=${accessToken}`,
      );
      return {
        accessToken,
        delHandler,
      };
    }

    const result = await this.getAccessToken(options);

    this.cache.set(key, result.accessToken, result.expireTime);

    console.info(
      `[open-api-client] get accessToken from remote api accessToken=${result.accessToken}`,
    );

    return {
      accessToken: result.accessToken || '',
      delHandler,
    };
  }

  private async getAccessToken(options: Options): Promise<{
    expireTime: number;
    accessToken: string;
  }> {
    let url = `${options.host}/token/get?appKey=${options.appKey}`;
    if (options.secretKey) {
      url = `${url}&secretKey=${options.secretKey}`;
    }

    const response = await axios.get(url);
    const res: {
      code: number;
      result: { expireTime: number; accessToken: string };
    } = response.data;

    if (res.code !== 0) {
      throw new Error(`Failed to get access token: ${JSON.stringify(res)}`);
    }

    return res.result;
  }
}

// 图片处理工具类
export class ImageProcessor {
  /**
   * 检查是否为快手文档图片API链接
   */
  static isKuaishouDocImageUrl(url: string): boolean {
    return url.includes('docs.corp.kuaishou.com/image/api/convert/loadimage');
  }

  /**
   * 从快手文档图片URL中提取imageId和docId
   */
  static extractKuaishouImageParams(
    imageUrl: string,
  ): { imageId: string; docId: string } | null {
    try {
      const url = new URL(imageUrl);
      const imageId = url.searchParams.get('id');

      // 从URL路径或其他参数中提取docId
      // 如果URL中没有docId参数，可能需要从其他地方获取
      let docId = url.searchParams.get('docId');

      if (!imageId) {
        console.error('无法从URL中提取imageId:', imageUrl);
        return null;
      }

      // 如果没有docId，可能需要从上下文或其他方式获取
      if (!docId) {
        console.warn('URL中未找到docId，可能需要从其他地方获取:', imageUrl);
        // 这里可以设置一个默认值或从其他地方获取
        docId = '';
      }

      return { imageId, docId };
    } catch (error) {
      console.error('解析快手文档图片URL失败:', error);
      return null;
    }
  }

  /**
   * 处理快手文档图片API链接 - 使用OpenAPI调用方式
   */
  static async processKuaishouDocImage(
    imageUrl: string,
    docId?: string,
  ): Promise<string> {
    try {
      // 提取图片参数
      const params = this.extractKuaishouImageParams(imageUrl);
      if (!params) {
        throw new Error('无法从URL中提取必要的参数');
      }

      // 如果传入了docId参数，使用传入的值
      const finalDocId = docId || params.docId;
      if (!finalDocId) {
        throw new Error('缺少docId参数，无法获取图片');
      }

      // 使用OpenAPI客户端调用图片接口
      const response = await openApiClient.request<any>({
        url: `/image/e/api/loadimage?imageId=${params.imageId}&docId=${finalDocId}`,
        method: 'GET',
        headers: {
          Accept: 'image/*,*/*',
        },
        options: getDocsAPIConfig(),
        responseType: 'arraybuffer',
      });

      // 处理响应数据 - 当responseType为arraybuffer时，response应该直接是ArrayBuffer
      let buffer: Buffer;
      let mimeType = 'image/png';

      if (response instanceof ArrayBuffer) {
        buffer = Buffer.from(response);
      } else if (response instanceof Buffer) {
        buffer = response;
      } else {
        throw new Error('无法处理OpenAPI响应数据格式，期望ArrayBuffer或Buffer');
      }

      // 检查是否是有效的图片数据
      if (buffer.length === 0) {
        throw new Error('获取到的图片数据为空');
      }

      // 检查文件头来确定图片类型
      const header = buffer.toString('hex', 0, 8).toUpperCase();
      if (header.startsWith('89504E47')) {
        mimeType = 'image/png';
      } else if (header.startsWith('FFD8FF')) {
        mimeType = 'image/jpeg';
      } else if (header.startsWith('47494638')) {
        mimeType = 'image/gif';
      } else if (header.startsWith('52494646')) {
        mimeType = 'image/webp';
      }

      const base64 = buffer.toString('base64');

      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      console.error('快手文档图片转换失败:', error);

      // 如果是OpenAPI相关错误，提供更详细的错误信息
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.error('OpenAPI认证失败，状态码:', error.response.status);
        throw new Error(`快手文档图片OpenAPI认证失败: 请检查API配置`);
      }

      throw new Error(`快手文档图片转换失败: ${error.message}`);
    }
  }

  /**
   * 将图片URL转换为base64格式
   */
  static async urlToBase64(imageUrl: string, docId?: string): Promise<string> {
    try {
      // 检查是否为快手文档图片，使用特殊处理
      if (this.isKuaishouDocImageUrl(imageUrl)) {
        return await this.processKuaishouDocImage(imageUrl, docId);
      }

      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000, // 30秒超时
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
      });

      const buffer = Buffer.from(response.data);

      // 根据响应头或URL扩展名确定MIME类型
      let mimeType = response.headers['content-type'];
      if (!mimeType) {
        const ext = path.extname(imageUrl).toLowerCase();
        switch (ext) {
          case '.jpg':
          case '.jpeg':
            mimeType = 'image/jpeg';
            break;
          case '.png':
            mimeType = 'image/png';
            break;
          case '.gif':
            mimeType = 'image/gif';
            break;
          case '.webp':
            mimeType = 'image/webp';
            break;
          default:
            mimeType = 'image/jpeg'; // 默认
        }
      }

      const base64 = buffer.toString('base64');

      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      console.error('图片转换失败:', error);
      throw new Error(`图片转换失败: ${error.message}`);
    }
  }

  /**
   * 将本地图片文件转换为base64格式
   */
  static async fileToBase64(filePath: string): Promise<string> {
    try {
      const buffer = await fs.readFile(filePath);
      const ext = path.extname(filePath).toLowerCase();

      let mimeType: string;
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          mimeType = 'image/jpeg';
          break;
        case '.png':
          mimeType = 'image/png';
          break;
        case '.gif':
          mimeType = 'image/gif';
          break;
        case '.webp':
          mimeType = 'image/webp';
          break;
        default:
          mimeType = 'image/jpeg';
      }

      const base64 = buffer.toString('base64');
      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      console.error('本地图片转换失败:', error);
      throw new Error(`本地图片转换失败: ${error.message}`);
    }
  }

  /**
   * 从文本内容中提取图片链接
   */
  static extractImageUrls(content: string): string[] {
    const imageUrls: string[] = [];

    // 匹配Markdown格式的图片: ![alt](url)
    const markdownImageRegex = /!\[.*?\]\((https?:\/\/[^\s\)]+)/g;
    let match: RegExpExecArray | null;
    while ((match = markdownImageRegex.exec(content)) !== null) {
      imageUrls.push(match[1]);
    }

    // 匹配HTML格式的图片: <img src="url">
    const htmlImageRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
    while ((match = htmlImageRegex.exec(content)) !== null) {
      if (match[1].startsWith('http')) {
        imageUrls.push(match[1]);
      }
    }

    // 匹配直接的URL链接（以图片扩展名结尾）
    const directUrlRegex =
      /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp|bmp|svg))/gi;
    while ((match = directUrlRegex.exec(content)) !== null) {
      imageUrls.push(match[1]);
    }

    // 匹配快手文档图片API链接格式
    const kuaishouImageRegex =
      /https?:\/\/docs\.corp\.kuaishou\.com\/image\/api\/convert\/loadimage\?[^)\s]+/gi;
    while ((match = kuaishouImageRegex.exec(content)) !== null) {
      imageUrls.push(match[0]); // 使用完整匹配而不是捕获组
    }

    // 匹配其他可能的图片API链接格式
    const imageApiRegex =
      /https?:\/\/[^\s]+\/(?:image|img|pic|photo|media)\/[^\s]*(?:\?[^\s]*)?/gi;
    while ((match = imageApiRegex.exec(content)) !== null) {
      const url = match[0]; // 使用完整匹配
      if (!url.includes('docs.corp.kuaishou.com/image/api/convert/loadimage')) {
        imageUrls.push(url);
      }
    }

    // 清理URL并去重
    return [...new Set(imageUrls.map((url) => url.trim().replace(/\)$/, '')))];
  }

  /**
   * 处理文档内容中的图片，将图片URL转换为base64并替换原内容
   */
  static async processImagesInContent(
    content: string,
    docId?: string,
  ): Promise<{
    processedContent: string;
    images: Array<{ url: string; base64: string; error?: string }>;
  }> {
    const imageUrls = this.extractImageUrls(content);
    const images: Array<{ url: string; base64: string; error?: string }> = [];
    let processedContent = content;

    for (const imageUrl of imageUrls) {
      try {
        const base64 = await this.urlToBase64(imageUrl, docId);
        images.push({ url: imageUrl, base64 });

        // 在内容中添加base64图片信息
        const imageInfo = `\n\n[图片已转换为base64格式: ${imageUrl}]\n`;
        processedContent += imageInfo;
      } catch (error) {
        console.error(`处理图片失败 ${imageUrl}:`, error);
        images.push({
          url: imageUrl,
          base64: '',
          error: error.message,
        });
      }
    }

    return { processedContent, images };
  }
}

// 创建默认实例
export const openApiClient = new OpenApiClient();

// 从环境变量获取配置
export function getDocsAPIConfig(): Options {
  return {
    host: process.env.DOCS_API_HOST || 'https://is-gateway.corp.kuaishou.com',
    appKey:
      process.env.DOCS_API_APP_KEY || '618b78c0-160e-4b08-92d6-96edb3b2ebfc',
    secretKey:
      process.env.DOCS_API_SECRET_KEY || '48c92cac-ec3b-4331-bbcd-1526987aae9b',
  };
}

// 获取文档内容并返回结构化数据（包含图片信息）
export async function fetchDocumentWithImages(url: string): Promise<{
  content: string;
  images: Array<{ url: string; base64: string; error?: string }>;
}> {
  try {
    // 从URL中提取docId
    const urlParts = url.split('/');
    const docId = urlParts[urlParts.length - 1];

    if (!docId || docId.length === 0) {
      throw new Error('无法从URL中提取docId');
    }

    // 使用OpenApiClient获取文档内容
    const response = await openApiClient.request<any>({
      url: `/word/e/api/get_md_content?docId=${docId}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      options: getDocsAPIConfig(),
    });

    // 解析响应内容
    let content = '';
    if (typeof response === 'string') {
      content = response;
    } else if (response && response.data && response.data.content) {
      content = response.data.content;
    } else if (response && response.content) {
      content = response.content;
    } else {
      content = JSON.stringify(response);
    }

    // 处理文档中的图片，传递docId以便处理快手文档图片
    const { processedContent, images } =
      await ImageProcessor.processImagesInContent(content, docId);

    return {
      content: processedContent,
      images,
    };
  } catch (error) {
    console.error('Error fetching document with images:', error);
    throw new Error(`获取文档失败: ${error.message}`);
  }
}
