import { StateGraph, START, END, MemorySaver } from '@langchain/langgraph';

// 导入节点函数
import {
  coordinatorNode,
  toolsNode,
  prdParserNode,
  frontendPlannerNode,
  backendPlannerNode,
  prdFeedbackNode,
  plannerFeedbackNode,
} from './nodes';

// 导入类型和常量
import { StateAnnotation } from './types/state.types';
import { NodeNames } from './constants';

/**
 * 创建优化后的工作流图
 *
 * 新的工作流程：
 * 1. COORDINATOR - 判断输入类型和路由
 * 2. TOOLS - 执行工具调用（如文档获取）
 * 3. PRD_PARSER - 解析和验证PRD
 * 4. PRD_FEEDBACK - PRD反馈和修正
 * 5. FRONTEND_PLANNER - 前端任务规划
 * 6. BACKEND_PLANNER - 后端任务规划
 * 7. PLANNER_FEEDBACK - 最终审核和确认
 *
 * 优化特点：
 * - 前后端任务分离，支持并行开发
 * - 使用prompt engineering替代withStructuredOutput
 * - 模块化节点设计，易于维护和扩展
 * - 完善的错误处理和反馈机制
 */
export function createOptimizedGraph() {
  // 创建内存检查点
  const checkpointer = new MemorySaver();

  // 构建工作流图
  const graph = new StateGraph(StateAnnotation)
    // 添加协调器节点 - 入口节点
    .addNode(NodeNames.COORDINATOR, coordinatorNode, {
      ends: [NodeNames.TOOLS, NodeNames.PRD_PARSER, END],
    })

    // 添加工具节点 - 处理工具调用
    .addNode(NodeNames.TOOLS, toolsNode, {
      ends: [NodeNames.COORDINATOR, NodeNames.PRD_PARSER],
    })

    // 添加PRD解析节点 - 解析和验证PRD
    .addNode(NodeNames.PRD_PARSER, prdParserNode, {
      ends: [NodeNames.BACKEND_PLANNER, NodeNames.PRD_FEEDBACK],
    })

    // 添加PRD反馈节点 - 处理PRD不完整的情况
    .addNode(NodeNames.PRD_FEEDBACK, prdFeedbackNode, {
      ends: [NodeNames.BACKEND_PLANNER, NodeNames.PRD_PARSER],
    })

    // 添加后端规划节点 - 生成后端任务
    .addNode(NodeNames.BACKEND_PLANNER, backendPlannerNode, {
      ends: [NodeNames.FRONTEND_PLANNER],
    })
    // 添加前端规划节点 - 生成前端任务
    .addNode(NodeNames.FRONTEND_PLANNER, frontendPlannerNode, {
      ends: [NodeNames.PLANNER_FEEDBACK],
    })
    // 添加规划反馈节点 - 最终审核
    .addNode(NodeNames.PLANNER_FEEDBACK, plannerFeedbackNode, {
      ends: [NodeNames.FRONTEND_PLANNER, NodeNames.BACKEND_PLANNER, END],
    })

    // 设置起始边
    .addEdge(START, NodeNames.COORDINATOR)

    // 编译图
    .compile({ checkpointer });

  return graph;
}

// 默认导出优化后的图实例
export default createOptimizedGraph();

/**
 * 工作流程说明：
 *
 * 1. 用户输入 → COORDINATOR
 *    - 判断是否为PRD请求
 *    - 检查是否包含文档链接
 *    - 路由到相应的处理节点
 *
 * 2. 如果有文档链接 → TOOLS
 *    - 获取文档内容和图片
 *    - 返回COORDINATOR进行PRD判断
 *
 * 3. PRD请求 → PRD_PARSER
 *    - 使用优化的prompt解析PRD
 *    - 评估PRD完整性
 *    - 生成结构化PRD或反馈表单
 *
 * 4. 如果PRD不完整 → PRD_FEEDBACK
 *    - 等待用户补充信息
 *    - 根据反馈决定下一步
 *
 * 5. PRD完整 → FRONTEND_PLANNER
 *    - 分析前端需求
 *    - 生成前端开发任务
 *    - 考虑AMIS框架特点
 *
 * 6. 前端规划完成 → BACKEND_PLANNER
 *    - 分析后端需求
 *    - 生成后端开发任务
 *    - 考虑Groovy+Spring Boot技术栈
 *
 * 7. 后端规划完成 → INTEGRATION_PLANNER
 *    - 分析前后端集成需求
 *    - 生成集成、测试、部署任务
 *    - 建立任务依赖关系
 *
 * 8. 集成规划完成 → PLANNER_FEEDBACK
 *    - 展示完整的任务规划
 *    - 等待用户审核确认
 *    - 支持针对性修改
 *
 * 9. 用户确认 → END
 *    - 输出最终的任务列表
 *    - 包含前端、后端、集成三类任务
 */
