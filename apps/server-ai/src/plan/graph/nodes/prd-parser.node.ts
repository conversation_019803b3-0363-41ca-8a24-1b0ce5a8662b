import { Command } from '@langchain/langgraph';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { createStructuredLLM } from '../utils/llm.utils';
import { NodeNames, PRDCompleteness } from '../constants';
import { StateAnnotation, PRDParserResponse } from '../types/state.types';
import {
  createMessageWithImages,
  createErrorMessage,
} from '../utils/message.utils';

/**
 * 可扩展的PRD要求配置
 */
interface PRDRequirementConfig {
  frontend: {
    [key: string]: {
      label: string;
      description: string;
      required: boolean;
      controlType?: string; // 控件类型：input-text, select, textarea, radio, checkbox等
      options?: Array<{ value: string; label: string }>; // 用于select、radio、checkbox等的选项
      source?: string | { url: string; method?: string; data?: any }; // 数据源URL或配置对象
    };
  };
  backend: {
    [key: string]: {
      label: string;
      description: string;
      required: boolean;
      controlType?: string;
      options?: Array<{ value: string; label: string }>;
      source?: string | { url: string; method?: string; data?: any };
    };
  };
  common: {
    [key: string]: {
      label: string;
      description: string;
      required: boolean;
      controlType?: string;
      options?: Array<{ value: string; label: string }>;
      source?: string | { url: string; method?: string; data?: any };
    };
  };
}

/**
 * 默认的PRD要求配置 - 可以根据需要扩展
 */
const DEFAULT_PRD_REQUIREMENTS: PRDRequirementConfig = {
  frontend: {
    page_name: {
      label: '页面名称',
      description: '页面名称',
      required: true,
      controlType: 'textarea',
    },
    // user_interaction: {
    //   label: '用户交互、组件交互',
    //   description: '用户交互流程 或者 组件联动流程的说明和操作逻辑',
    //   required: true,
    //   controlType: 'textarea',
    // },
  },
  backend: {
    // datasource: {
    //   label: '数据表依赖',
    //   description: '依赖的数据表、数据信息',
    //   required: true,
    //   controlType: 'textarea',
    // },
    // api_dependencies: {
    //   label: '组件对接口的依赖',
    //   description: 'API接口定义和规范',
    //   required: true,
    //   controlType: 'textarea',
    // },
  },
  common: {},
};

/**
 * 生成可扩展的PRD要求描述
 */
function generateRequirementsDescription(config: PRDRequirementConfig): string {
  const sections = [
    {
      title: '前端要求',
      key: 'frontend' as keyof PRDRequirementConfig,
    },
    {
      title: '后端要求',
      key: 'backend' as keyof PRDRequirementConfig,
    },
    {
      title: '通用要求',
      key: 'common' as keyof PRDRequirementConfig,
    },
  ];

  return sections
    .map((section) => {
      const requirements = config[section.key];
      if (!requirements || Object.keys(requirements).length === 0) {
        return `**${section.title}：** 无要求`;
      }
      const requiredItems = Object.entries(requirements)
        .filter(([, req]) => req.required)
        .map(([, req]) => `- ${req.description}`)
        .join('\n');

      const optionalItems = Object.entries(requirements)
        .filter(([, req]) => !req.required)
        .map(([, req]) => `- ${req.description} (可选)`)
        .join('\n');

      let sectionContent = `**${section.title}：**\n${requiredItems}`;
      if (optionalItems) {
        sectionContent += `\n${optionalItems}`;
      }

      return sectionContent;
    })
    .join('\n\n');
}

/**
 * 生成可扩展的XML模板
 */
function generateXMLTemplate(): string {
  return `
<page class="data-management-system">
    <alert></alert>
    <!-- 筛选区域 -->
    <filterSection class="filter-container">
        <!-- 顶部搜索区 -->
        <searchArea class="search-container">
            <inputGroup>
                <select placeholder="选择搜索类型"/>
                <input type="text" placeholder="输入关键词搜索"/>
                <otherControls></otherControls>
            </inputGroup>
        </searchArea>

        <!-- 筛选器区 -->
        <filterArea class="filter-rows">
            <!-- 作者管理类目筛选行 -->
            <filterRow class="filter-row">
                <label>作者管理类目:</label>
                <horizontalSelect mode="multiple">
                    <option value="all">全部</option>
                    <option value="news">时政资讯</option>
                    <!-- 其他选项 -->
                </horizontalSelect>
            </filterRow>

            <!-- 视频词图类目筛选行 -->
            <filterRow class="filter-row">
                <label>视频词图类目:</label>
                <cascadeSelect>
                    <option value="all">全部</option>
                    <!-- 级联选项 -->
                </cascadeSelect>
            </filterRow>

            <!-- 高级筛选行 -->
            <otherFilterRow class="filter-row">
                <label>xxx:</label>
                <radioGroup>
                    <radio value="all">全部</radio>
                    <radio value="social">xx</radio>
                    <radio value="weather">xx</radio>
                </radioGroup>
            </otherFilterRow>

        </filterArea>

        <!-- 已选区域 -->
        <selectedFilters class="selected-tags">
            <tag closable>时政资讯</tag>
            <tag closable>社会事件</tag>
            <!-- 其他已选标签 -->
        </selectedFilters>
    </filterSection>

    <!-- 表格区域 -->
    <tableSection class="table-container">
        <!-- 表格头部 -->
        <tableHeader class="table-header">
            <div class="left">共 2 个视频</div>
            <div class="right">
                <button>下载明细</button>
            </div>
        </tableHeader>

        <!-- 表格主体 -->
        <table class="data-table">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>视频</th>
                    <th>有效播放次数</th>
                    <th>点赞次数</th>
                    <th>评论次数</th>
                    <th>分享次数</th>
                    <th>收藏次数</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 数据行 -->
                <tr>
                    <td>1</td>
                    <td>
                        <videoCard>
                            <thumbnail/>
                            <videoInfo/>
                        </videoCard>
                    </td>
                    <!-- 其他数据列 -->
                    <td>
                        <actionButtons>
                            <button>视频详情</button>
                            <button>提报热点</button>
                            <!-- 其他操作按钮 -->
                        </actionButtons>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- 分页器 -->
        <pagination class="table-pagination">
            <pageSize/>
            <pageNumber/>
            <totalCount/>
        </pagination>
    </tableSection>
</page>`;
}

/**
 * 生成PRD解析提示
 */
function generatePrompt(
  state: typeof StateAnnotation.State,
  requirementsDescription: string,
  xmlTemplate: string,
  requirementsConfig: PRDRequirementConfig,
): string {
  // 检查是否有用户反馈信息
  const hasUserFeedback = state.prdFeedback && state.prdFeedback.trim() !== '';

  // 构建完整的PRD内容
  let fullPRDContent = state.originalPRD;
  if (hasUserFeedback) {
    fullPRDContent += `\n\n## 用户补充信息\n${state.prdFeedback}`;
  }

  return `
你是一个专业的全栈产品经理和技术架构师，具备丰富的前后端开发经验。你的任务是分析用户输入的产品需求描述，评估其完整性，并提供相应的处理建议。

${
  hasUserFeedback
    ? `## 处理说明
这是一个补充完善的PRD分析请求。用户已经基于之前的反馈提供了额外的信息，请重新评估PRD的完整性。

## 原始PRD内容
${state.originalPRD}

## 用户补充的信息
${state.prdFeedback}

请将补充信息与原始内容结合，重新评估整体PRD的完整性。
`
    : `## 用户输入的PRD内容
${state.originalPRD}`
}

${
  state.images && state.images.length > 0
    ? `
## 附加的图片信息
用户提供了以下图片作为PRD的补充材料：
${state.images.map((img, index) => `图片${index + 1}: ${img.url}${img.error ? ` (处理失败: ${img.error})` : ' (已处理)'}`).join('\n')}
请结合图片内容进行PRD分析。如果图片包含原型图、界面设计、流程图或功能说明，请在分析中重点考虑这些视觉信息。
`
    : ''
}

## 分析任务
请按照以下标准评估PRD的完整性，并提供相应的响应：

### 完整性评估标准
一个完整的PRD应该包含：

${requirementsDescription}

## 响应格式要求

请统一使用以下XML格式进行响应：

### 如果PRD完整
<status>您的产品需求文档已经完整！我已经为您生成了结构化的PRD文档。点击下方的文件可以查看详细内容。</status>
<file name="structured-prd.xml" filetype="xml">
${xmlTemplate}
</file>

### 如果PRD不完整
<status>您提供的产品需求文档还需要完善，缺少以下重要信息：
[在这里详细描述缺少的信息，比如：
- xxx

为了帮助您完善需求，我准备了一个表单来收集这些信息。</status>
<amis_json>
{
  "type": "form",
  "title": "完善产品需求文档",
  "submitText": "提交完善信息",
  "body": [
    根据缺失的信息类别动态创建表单字段
  ]
}
</amis_json>

生成AMIS表单时，请根据缺失的信息类别动态创建表单字段。参考以下字段映射：

${
  Object.entries(requirementsConfig.frontend).length > 0
    ? `**前端相关字段：**
${Object.entries(requirementsConfig.frontend)
  .map(([key, req]) => {
    let result = `- ${key}: ${req.label} - ${req.description}`;
    if (req.controlType) result += ` (控件类型: ${req.controlType})`;
    if (req.options) result += ` (预设选项可用)`;
    if (req.source) result += ` (数据源可用)`;
    return result;
  })
  .join('\n')}
    `
    : ''
}

${
  Object.entries(requirementsConfig.backend).length > 0
    ? `**后端相关字段：**
${Object.entries(requirementsConfig.backend)
  .map(([key, req]) => {
    let result = `- ${key}: ${req.label} - ${req.description}`;
    if (req.controlType) result += ` (控件类型: ${req.controlType})`;
    if (req.options) result += ` (预设选项可用)`;
    if (req.source) result += ` (数据源可用)`;
    return result;
  })
  .join('\n')}
    `
    : ''
}

${
  Object.entries(requirementsConfig.common).length > 0
    ? `**通用字段：**
${Object.entries(requirementsConfig.common)
  .map(([key, req]) => {
    let result = `- ${key}: ${req.label} - ${req.description}`;
    if (req.controlType) result += ` (控件类型: ${req.controlType})`;
    if (req.options) result += ` (预设选项可用)`;
    if (req.source) result += ` (数据源可用)`;
    return result;
  })
  .join('\n')}
    `
    : ''
}

AMIS表单字段示例：
\`\`\`json
{
  "name": "ui_design",
  "type": "textarea",
  "label": "页面布局",
  "description": "请详细描述页面布局，或上传原型图",
  "required": true,
  "placeholder": "请描述页面的整体布局、主要区域划分、组件位置等"
}
\`\`\`

## 重要提示
1. 统一使用XML格式响应，不要使用JSON格式
2. 如果PRD完整，在<file>标签中提供结构化的XML文档
3. 如果PRD不完整，在<amis_json>标签中提供表单配置
4. 确保XML格式正确，可以被解析
5. 如果有图片信息，请在分析中充分考虑视觉内容
6. 优先考虑技术实现的可行性和完整性
7. 对于模糊或不明确的需求，归类为不完整
8. 根据配置的要求标准进行评估，必需项缺失时标记为不完整
${hasUserFeedback ? '9. 重点关注用户补充的信息，确保这些信息已经满足了之前缺失的要求' : ''}

现在请开始分析并返回结果：
`;
}

/**
 * PRD解析器节点 - 处理和验证PRD内容
 * 负责将原始PRD转换为结构化格式并评估完整性
 */
export async function prdParserNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  const llm = createStructuredLLM();

  // 使用默认配置
  const requirementsConfig = DEFAULT_PRD_REQUIREMENTS;
  const requirementsDescription =
    generateRequirementsDescription(requirementsConfig);
  const xmlTemplate = generateXMLTemplate();

  // 检查是否有用户反馈信息
  const hasUserFeedback = state.prdFeedback && state.prdFeedback.trim() !== '';

  // 添加处理日志
  console.log('PRD Parser 节点开始处理:', {
    hasUserFeedback,
    prdFeedbackLength: state.prdFeedback?.length || 0,
    originalPRDLength: state.originalPRD?.length || 0,
    processingStatus: state.processingStatus,
    hasImages: state.images && state.images.length > 0,
  });

  // 构建优化的PRD解析提示
  const promptText = generatePrompt(
    state,
    requirementsDescription,
    xmlTemplate,
    requirementsConfig,
  );

  try {
    // 使用LLM进行PRD分析
    let response: AIMessage;
    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(promptText, state.images);
      response = await llm.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await llm.invoke(promptText);
    }
    console.log('promptText', { promptText: promptText.toString(), response });

    // 解析LLM响应
    const responseContent =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    // 统一解析XML格式响应
    if (!responseContent.includes('<status>')) {
      throw new Error('LLM响应格式不正确，缺少status标签');
    }

    // 检查是否包含file标签（PRD完整的情况）
    if (responseContent.includes('<file')) {
      // 提取file标签中的内容作为结构化PRD
      const fileMatch = responseContent.match(/<file[^>]*>([\s\S]*?)<\/file>/);
      if (!fileMatch) {
        throw new Error('无法提取file标签中的内容');
      }

      const structuredPRDContent = fileMatch[1].trim();

      return new Command({
        goto: NodeNames.FRONTEND_PLANNER,
        update: {
          structuredPRD: structuredPRDContent,
          prdAnalysis: responseContent, // 保存完整的XML响应
          // 清除已处理的反馈信息
          prdFeedback: '',
          processingStatus: hasUserFeedback
            ? 'PRD经用户补充后已完善，开始前端任务规划'
            : 'PRD分析完成，开始前端任务规划',
        },
      });
    }
    // 检查是否包含amis_json标签（PRD不完整的情况）
    else if (responseContent.includes('<amis_json>')) {
      return new Command({
        goto: NodeNames.PRD_FEEDBACK,
        update: {
          prdAnalysis: responseContent, // 直接传递XML格式的响应
          // 清除已处理的反馈信息
          prdFeedback: '',
          processingStatus: hasUserFeedback
            ? '用户补充信息已收到，但PRD仍需进一步完善'
            : 'PRD信息不完整，等待用户补充',
        },
      });
    }
    // 如果既没有file标签也没有amis_json标签，视为格式错误
    else {
      throw new Error('LLM响应格式不正确，缺少file或amis_json标签');
    }
  } catch (error) {
    console.error('PRD-Parser节点处理失败:', error);

    // 创建错误响应（XML格式）
    const errorResponseXML = `<status>系统处理您的需求时发生错误：${createErrorMessage(error)}

请重新描述您的产品需求，提供更详细的信息以便系统更好地理解和处理。</status>
<amis_json>
{
  "type": "form",
  "title": "系统错误 - 请重新提交",
  "submitText": "重新提交需求",
  "body": [
    {
      "type": "alert",
      "level": "danger",
      "body": "处理PRD时发生错误，请重新提交您的需求。"
    },
    {
      "name": "retry_prd",
      "type": "textarea",
      "label": "请重新描述您的产品需求",
      "required": true,
      "placeholder": "请提供更详细的产品需求描述，包括功能说明、界面要求、数据需求等...",
      "minRows": 5
    }
  ]
}
</amis_json>`;

    return new Command({
      goto: NodeNames.PRD_FEEDBACK,
      update: {
        prdAnalysis: errorResponseXML,
        error: createErrorMessage(error, 'PRD解析'),
        // 清除已处理的反馈信息
        prdFeedback: '',
        processingStatus: hasUserFeedback
          ? '处理用户补充信息时发生错误'
          : 'PRD解析过程中发生错误',
      },
    });
  }
}

/**
 * 创建自定义PRD要求配置的辅助函数
 */
export function createCustomPRDRequirements(
  customConfig: Partial<PRDRequirementConfig>,
): PRDRequirementConfig {
  return {
    frontend: {
      ...DEFAULT_PRD_REQUIREMENTS.frontend,
      ...customConfig.frontend,
    },
    backend: { ...DEFAULT_PRD_REQUIREMENTS.backend, ...customConfig.backend },
    common: { ...DEFAULT_PRD_REQUIREMENTS.common, ...customConfig.common },
  };
}

/**
 * 获取默认PRD要求配置
 */
export function getDefaultPRDRequirements(): PRDRequirementConfig {
  return DEFAULT_PRD_REQUIREMENTS;
}

/**
 * 验证PRD要求配置的有效性
 */
export function validatePRDRequirements(config: PRDRequirementConfig): boolean {
  const sections = ['frontend', 'backend', 'common'] as const;

  for (const section of sections) {
    const requirements = config[section];
    if (!requirements || typeof requirements !== 'object') {
      return false;
    }
    for (const [key, req] of Object.entries(requirements)) {
      if (!req.label || !req.description || typeof req.required !== 'boolean') {
        console.warn(`Invalid requirement configuration for ${section}.${key}`);
        return false;
      }
    }
  }

  return true;
}

/**
 * 导出类型定义供外部使用
 */
export type { PRDRequirementConfig };
