# 数据库性能优化指南

## 问题诊断

### "Out of sort memory" 错误
这个错误通常出现在以下情况：
- 表数据量很大，需要排序的记录数超过了MySQL的sort_buffer_size
- 缺少合适的索引，导致全表扫描后排序
- JSON字段过大，影响排序性能

## 解决方案

### 1. 立即解决 - 增加MySQL排序缓冲区

在MySQL配置文件 (`my.cnf` 或 `my.ini`) 中增加：

```ini
[mysqld]
# 增加排序缓冲区大小（默认256KB，建议设置为2MB-8MB）
sort_buffer_size = 2M

# 增加最大包大小（如果有大JSON字段）
max_allowed_packet = 64M

# 增加临时表大小
tmp_table_size = 64M
max_heap_table_size = 64M

# 启用查询缓存（适用于读多写少的场景）
query_cache_type = 1
query_cache_size = 64M
```

重启MySQL服务使配置生效：
```bash
sudo systemctl restart mysql
```

### 2. 数据库索引优化

运行我们提供的迁移脚本：
```bash
cd apps/server-ai
npm run migration:run
```

或手动执行索引创建：
```sql
-- 为threads表添加复合索引
CREATE INDEX `IDX_THREAD_STATUS_UPDATED_AT` ON `threads` (`status`, `updated_at` DESC);
CREATE INDEX `IDX_THREAD_USER_STATUS_UPDATED` ON `threads` (`user_id`, `status`, `updated_at` DESC);
```

### 3. 查询优化

我们已经优化了 `ThreadService.getThreads()` 方法：
- 添加了错误处理和降级策略
- 限制了单次查询的最大记录数
- 只选择必要的字段，减少数据传输量
- 分离了计数查询和数据查询

### 4. 数据清理

定期清理无用数据：
```bash
# 使用我们提供的清理脚本
mysql -u username -p database_name < scripts/cleanup-threads.sql
```

### 5. 监控和预警

添加监控查询来跟踪表大小：
```sql
-- 检查threads表大小和记录数
SELECT 
  COUNT(*) as total_records,
  ROUND(AVG(LENGTH(messages)), 2) as avg_messages_size,
  ROUND(AVG(LENGTH(tasks)), 2) as avg_tasks_size,
  ROUND(SUM(LENGTH(messages) + LENGTH(tasks)) / 1024 / 1024, 2) as total_json_mb
FROM threads;
```

## 性能基准

### 优化前
- 查询threads表时出现"Out of sort memory"错误
- 排序操作超时
- 前端无法加载会话列表

### 优化后预期效果
- 查询响应时间 < 100ms
- 支持更大的数据量
- 前端流畅加载会话列表

## 最佳实践

### 1. 分页策略
```typescript
// 推荐的分页参数
{
  page: 1,
  limit: 20, // 不要超过50
  status: 1   // 明确指定状态过滤
}
```

### 2. JSON字段优化
- 定期清理messages和tasks中的无用数据
- 考虑将大JSON字段移到单独的表
- 使用压缩存储大文本内容

### 3. 索引维护
```sql
-- 定期分析表
ANALYZE TABLE threads;

-- 检查索引使用情况
SHOW INDEX FROM threads;

-- 查看查询执行计划
EXPLAIN SELECT * FROM threads WHERE status = 1 ORDER BY updated_at DESC LIMIT 10;
```

## 应急处理

如果问题仍然存在，可以临时使用以下SQL直接查询：
```sql
-- 不排序的简单查询（应急方案）
SELECT id, title, status, created_at, updated_at 
FROM threads 
WHERE status = 1 
LIMIT 10;
```

## 长期优化建议

1. **数据分表**: 如果threads表继续增长，考虑按时间分表
2. **读写分离**: 对于查询密集的场景，可以配置MySQL主从复制
3. **缓存策略**: 将热门查询结果缓存到Redis
4. **归档策略**: 定期将旧数据归档到历史表

## 监控指标

定期检查以下指标：
- `SHOW PROCESSLIST;` - 查看正在执行的查询
- `SHOW ENGINE INNODB STATUS;` - 查看InnoDB状态
- 表大小和索引大小的增长趋势
- 慢查询日志中的排序相关查询

---

如果问题持续存在，请联系数据库管理员或查看MySQL错误日志获取更多信息。 
