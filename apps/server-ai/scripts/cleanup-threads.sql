-- Threads表清理脚本
-- 用于清理旧的、无用的threads数据，减少表大小和提升查询性能

-- 1. 查看threads表的数据分布
SELECT
  status,
  COUNT(*) as count,
  MIN(created_at) as oldest,
  MAX(created_at) as newest
FROM threads
GROUP BY status;

-- 2. 查看threads表大小
SELECT
  table_name,
  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS "Size in MB"
FROM information_schema.tables
WHERE table_schema = DATABASE()
AND table_name = 'threads';

-- 3. 清理已删除状态的旧记录（可选，保留近30天的）
-- DELETE FROM threads
-- WHERE status = 0
-- AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 4. 清理超过3个月的已完成线程（可选）
-- DELETE FROM threads
-- WHERE status = 2
-- AND updated_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 5. 清理空的或无效的threads（messages和tasks都为空的处理中线程）
-- DELETE FROM threads
-- WHERE status = 1
-- AND JSON_LENGTH(messages) = 0
-- AND JSON_LENGTH(tasks) = 0
-- AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 6. 优化表（重建索引和回收空间）
-- OPTIMIZE TABLE threads;

-- 7. 分析表以更新统计信息
-- ANALYZE TABLE threads;

-- 执行清理后检查效果
SELECT
  'After cleanup' as status,
  COUNT(*) as total_threads,
  COUNT(CASE WHEN status = 1 THEN 1 END) as processing,
  COUNT(CASE WHEN status = 2 THEN 1 END) as completed,
  COUNT(CASE WHEN status = 0 THEN 1 END) as deleted
FROM threads;
