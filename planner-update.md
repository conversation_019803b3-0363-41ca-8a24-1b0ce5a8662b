# Planner

## 项目概述

Planner 是一个基于 LLM 的 PRD 处理和任务规划工具，目前已实现简单的对话功能，但缺乏持久化的数据存储。本方案旨在扩展 Planner 的功能，使其支持项目管理、会话状态、会话历史记录、任务列表、结构化 PRD 等数据的持久化存储。

## 系统架构

系统分为前端（Web）、后端（Backend）、数据库（DB）、LLM 服务、IDE 环境和 MCP 服务模块，各模块的主要功能和交互流程如下：

```mermaid
sequenceDiagram
    autonumber
    participant Dev as 开发者
    participant Web as Web前端
    participant LLM as LLM服务
    participant Backend as 后端系统
    participant DB as 数据库
    participant IDE as IDE环境
    participant MCP as MCP服务

    Dev->>Web: 输入原始PRD
    Web->>LLM: 传递PRD内容
    LLM-->>Web: 返回缺失信息的询问
    Web-->>Dev: 显示询问
    Dev->>Web: 提供补充信息
    Web->>LLM: 传递补充信息
    LLM-->>Web: 返回结构化PRD和任务列表
    Web->>Backend: 创建会话记录
    Backend->>DB: 存储会话数据
    DB-->>Backend: 确认存储成功
    Backend-->>Web: 返回会话ID
    Web-->>Dev: 展示结果和MCP调用prompt

    Dev->>IDE: 复制prompt开始开发
    Note over Dev,IDE: 开发和调试阶段

    IDE->>MCP: 调用init获取会话上下文
    MCP->>Backend: 请求会话数据
    Backend->>DB: 读取会话数据
    DB-->>Backend: 返回会话数据
    Backend-->>MCP: 返回会话上下文
    MCP-->>IDE: 显示会话上下文

    Note over IDE,MCP: 执行开发任务
    IDE->>MCP: 调用run_task执行任务

    Note over IDE,MCP: 完成后更新状态
    IDE->>MCP: 调用update更新部署信息
    MCP->>Backend: 更新会话状态和部署信息
    Backend->>DB: 更新数据库记录
    DB-->>Backend: 确认更新成功
    Backend-->>MCP: 确认状态更新
    MCP-->>IDE: 返回更新结果

    Dev->>Web: 查看任务状态
    Web->>Backend: 请求最新状态
    Backend->>DB: 读取状态数据
    DB-->>Backend: 返回状态数据
    Backend-->>Web: 返回已完成状态和页面链接
    Web-->>Dev: 展示任务完成和线上链接
```

## 技术栈

- **前端**：React 19 + TypeScript + Vite + Zustand + Tailwind CSS + Radix UI + @ai-sdk/react + CopilotKit
- **后端**：NestJS + TypeScript + MySQL + TypeORM + Redis
- **AI Agent**：LangGraph + LangChain + @langchain/openai (通过快手 AI Gateway)
- **MCP Server**：@modelcontextprotocol/sdk + TypeScript
- **开发工具**：ESLint + Prettier + Turbo (Monorepo 管理)

## 功能模块

### 1. 会话管理模块

- **多会话支持**：每个 PRD 对应一个独立会话，支持会话的创建、删除、切换
- **会话状态管理**：需求分析中、任务规划中、开发中、已完成等状态
- **会话持久化**：会话信息、对话历史、任务状态的数据库存储
- **会话恢复**：支持从任意历史状态恢复会话继续工作

### 2. AI 对话模块

- **多轮对话**：支持连续的 AI 对话，保持上下文记忆
- **流式响应**：实时显示 AI 回复内容，提升用户体验
- **多样化展现**：
  - `message`类型：支持 Markdown 渲染，代码高亮
  - `interrupt`类型：支持表单交互，收集用户反馈
  - `tools`类型：显示 AI 工具调用过程和结果
- **消息管理**：消息的存储、检索、导出功能

### 3. 任务管理模块

- **任务生成**：基于结构化 PRD 自动生成前端、后端、集成任务
- **任务状态**：未开始、进行中、已完成、已部署等状态管理
- **依赖关系**：任务间的依赖关系可视化和管理
- **MCP 集成**：为每个任务生成 MCP 调用提示，支持 IDE 集成开发
- **任务更新**：支持任务内容的 AI 重新生成和手动编辑

### 4. AI Agent 模块

- **PRD 解析**：智能解析原始 PRD，识别关键信息和缺失内容
- **需求完善**：通过多轮交互完善 PRD 信息
- **结构化输出**：生成标准化的结构化 PRD 文档
- **任务规划**：基于技术栈和业务需求生成详细任务列表
- **工作流编排**：使用 LangGraph 实现复杂的 AI 工作流

### 5. MCP Server 模块

- **IDE 集成**：提供与 IDE 的标准化接口
- **任务同步**：支持任务状态的双向同步
- **开发工具**：提供 init、run_task、update 等开发工具
- **部署管理**：支持开发完成后的部署信息更新

## 开发方案

### 1. 前端开发 (apps/client-planner)

**当前进度**：✅ 基础框架搭建完成，包含会话管理、AI 对话、任务展示等核心组件

**开发任务**：

- **会话管理优化** (优先级: High)

  - 完善会话列表的 CRUD 操作
  - 实现会话状态的实时更新
  - 添加会话搜索和筛选功能
  - 优化会话切换的用户体验

- **AI 对话界面增强** (优先级: High)

  - 完善消息渲染器，支持更多消息类型
  - 实现 interrupt 类型的表单交互
  - 添加消息导出和分享功能
  - 优化流式响应的显示效果

- **任务管理界面** (优先级: Medium)

  - 实现任务列表的可视化展示
  - 添加任务状态更新功能
  - 实现任务依赖关系的图形化展示
  - 集成 MCP 调用提示的复制功能

- **状态管理完善** (优先级: Medium)
  - 使用 Zustand 优化全局状态管理
  - 实现数据的本地缓存和同步
  - 添加错误处理和重试机制

**技术要点**：

- 使用@ai-sdk/react + CopilotKit 处理 AI 交互
- 集成 React Hook Form + Zod 进行表单验证
- 使用 Radix UI + Tailwind CSS 构建现代化 UI

### 2. 后端 API 开发 (apps/server-ai/src/plan)

**当前进度**：✅ NestJS 框架搭建完成，数据库实体和 API 模块结构清晰

**开发任务**：

- **Chat API 完善** (优先级: High)

  - 完善流式对话接口，支持 LangGraph 工作流
  - 实现消息的持久化存储和检索
  - 添加会话上下文管理
  - 集成 AI Agent 的工作流调用

- **Thread API 开发** (优先级: High)

  - 实现会话的完整 CRUD 操作
  - 添加会话状态管理接口
  - 实现会话数据的关联查询
  - 添加会话统计和分析功能

- **Task API 开发** (优先级: Medium)

  - 实现任务的生成、更新、状态管理
  - 添加任务依赖关系的处理
  - 集成 AI 重新生成任务功能
  - 实现任务的批量操作

- **PRD API 开发** (优先级: Medium)

  - 实现 PRD 的版本管理
  - 添加结构化 PRD 的生成和存储
  - 实现 PRD 内容的 AI 分析接口

- **数据库优化** (优先级: Low)
  - 添加数据库索引优化查询性能
  - 实现数据的软删除和恢复
  - 添加数据备份和迁移脚本

**技术要点**：

- 使用 TypeORM 进行数据库操作
- 集成 LangGraph 进行 AI 工作流编排
- 实现 RESTful API 和实时通信

### 3. AI Agent 优化 (apps/server-ai/plan/graph)

**当前进度**：✅ LangGraph 基础集成完成，支持 PRD 解析和任务生成

**开发任务**：

- **工作流优化** (优先级: High)

  - 优化前后端任务生成的准确性
  - 完善任务依赖关系的分析
  - 添加任务优先级的智能排序
  - 实现任务内容的迭代优化

- **Prompt Engineering** (优先级: High)

  - 优化各节点的提示词模板
  - 添加少样本学习示例
  - 实现动态提示词调整
  - 完善错误处理和重试机制

- **工具集成** (优先级: Medium)
  - 完善文档获取工具
  - 添加代码分析工具
  - 集成项目模板生成工具
  - 实现任务验证工具

**技术要点**：

- 使用 LangGraph 进行复杂工作流编排
- 集成@langchain/openai 通过快手 AI Gateway
- 实现结构化输出和错误处理

### 4. MCP Server 开发 (apps/mcp-planner)

**当前进度**：❌ 尚未开始开发

**开发任务**：

- **项目初始化** (优先级: Low)

  - 搭建 MCP Server 基础框架
  - 配置 TypeScript 开发环境
  - 集成@modelcontextprotocol/sdk

- **核心工具开发** (优先级: Low)

  - 实现 init 工具：获取会话上下文
  - 实现 run_task 工具：执行开发任务
  - 实现 update 工具：更新任务状态和部署信息
  - 实现 sync 工具：同步任务进度

- **IDE 集成** (优先级: Low)
  - 提供标准化的 MCP 接口
  - 实现与后端 API 的通信
  - 添加错误处理和日志记录

**技术要点**：

- 使用@modelcontextprotocol/sdk 构建 MCP 服务
- 实现与后端 API 的 HTTP 通信
- 提供标准化的 IDE 集成接口

## 架构设计详解

### 数据流架构

```mermaid
graph TB
    subgraph "前端层"
        A[React组件] --> B[Zustand状态管理]
        B --> C[API客户端]
    end

    subgraph "后端层"
        D[NestJS控制器] --> E[业务服务层]
        E --> F[数据访问层]
        E --> G[AI Agent服务]
    end

    subgraph "数据层"
        H[MySQL数据库]
        I[Redis缓存]
    end

    subgraph "AI层"
        J[LangGraph工作流]
        K[LangChain工具]
        L[快手AI Gateway]
    end

    subgraph "IDE集成层"
        M[MCP Server]
        N[IDE插件]
    end

    C --> D
    F --> H
    F --> I
    G --> J
    J --> K
    K --> L
    M --> E
    N --> M
```

### 核心实体关系

```mermaid
erDiagram
    THREADS {
        string id PK "会话ID"
        string user_id FK "用户ID"
        string title "会话标题"
        string prdLink "原始PRD"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
        json metadata "元数据"
        enum status "状态(0:deleted/1:processing/2:completed)"
        json messages "消息列表"
        json state "graph状态"
        json tasks "任务列表"
    }
```

### API 接口设计

#### 会话管理 API

- `GET /api/plan/getThreads` - 获取会话列表
- `POST /api/plan/createThread` - 创建新会话
- `GET /api/plan/getThread?threadId=<threadId>` - 获取会话详情
- `POST /api/plan/updateThread` - 更新会话信息
- `POST /api/plan/updateTask` - 更新任务信息
- `DELETE /api/plan/deleteTask` - 删除任务
- `DELETE /api/plan/deleteThread` - 删除会话

#### 对话 API

- `POST /api/plan/stream` - 流式对话接口
- `POST /api/plan/stream/regenerate` - 重新生成消息的 AI 回复

### 开发阶段规划

#### 第一阶段：核心对话功能

**目标**：实现基本的 AI 对话功能

- 完善 对话 API 的流式接口
- 优化前端 AI 对话界面
- 集成 LangGraph 工作流
- 实现消息的持久化存储

**交付物**：
- 可用的 AI 对话界面
- 稳定的流式响应
- 基本的会话管理

#### 第二阶段：会话和任务管理

**目标**：完善会话管理和任务生成

- 实现完整的 Thread API
- 开发 Task API 和任务生成逻辑
- 完善前端会话管理界面
- 实现任务列表展示

**交付物**：

- 完整的会话管理功能
- 自动化任务生成
- 任务状态管理

#### 第三阶段：功能完善和优化

**目标**：完善所有核心功能

- 实现 PRD API 和结构化处理
- 优化 AI Agent 的任务生成效果
- 完善前端用户体验
- 添加错误处理和性能优化

**交付物**：

- 完整的 PRD 处理流程
- 优化的任务生成质量
- 完善的用户界面

#### 第四阶段：MCP 集成

**目标**：实现 IDE 集成功能

- 开发 MCP Server
- 实现 IDE 工具集成

**交付物**：

- 可用的 MCP Server

### 开发环境启动

```bash
# 安装依赖
pnpm install

# 启动前端服务
pnpm run start:planner
```

### 监控和日志

- **应用监控**：集成 NestJS 内置的健康检查
- **数据库监控**：使用 TypeORM 的查询日志
- **AI 服务监控**：监控 LangGraph 工作流的执行状态

### 性能优化

- **数据库优化**：添加适当的索引，使用连接池
- **缓存策略**：使用 Redis 缓存频繁查询的数据
- **前端优化**：使用 React.memo 和 useMemo 优化渲染
- **API 优化**：实现请求去重和防抖机制
