{"author": "<PERSON><PERSON><PERSON><PERSON>", "name": "@langchain/langgraph-checkpoint-mysql", "peerDependencies": {"@langchain/core": "^0.1.0", "@langchain/langgraph-checkpoint": "^0.0.1"}, "description": "LangGraph checkpointer that uses MySQL as the backing store", "main": "dist/index.js", "license": "MIT", "version": "0.1.0", "devDependencies": {"@types/jest": "^29.5.0", "jest": "^29.5.0", "typescript": "^5.0.4", "@types/node": "^18.15.11", "ts-jest": "^29.1.0"}, "keywords": ["langchain", "langgraph", "checkpoint", "mysql", "typeorm"], "scripts": {"build": "tsc", "test": "jest", "lint": "eslint src"}, "types": "dist/index.d.ts", "dependencies": {"@langchain/core": "^0.1.0", "@langchain/langgraph-checkpoint": "^0.0.1", "mysql2": "^3.6.0", "typeorm": "^0.3.17"}, "publishConfig": {"access": "public"}}