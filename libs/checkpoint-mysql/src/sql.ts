import { TASKS } from "@langchain/langgraph-checkpoint";

/**
 * SQL语句接口定义
 */
export interface SQL_STATEMENTS {
  SELECT_SQL: string;
  UPSERT_CHECKPOINT_BLOBS_SQL: string;
  UPSERT_CHECKPOINTS_SQL: string;
  UPSERT_CHECKPOINT_WRITES_SQL: string;
  INSERT_CHECKPOINT_WRITES_SQL: string;
}

/**
 * 获取SQL语句
 * @returns SQL语句对象
 */
export const getSQLStatements = (): SQL_STATEMENTS => {
  return {
    // 查询SQL
    SELECT_SQL: `SELECT
      thread_id,
      checkpoint,
      checkpoint_ns,
      checkpoint_id,
      parent_checkpoint_id,
      metadata,
      (SELECT JSON_ARRAYAGG(JSON_ARRAY(bl.channel, bl.type, bl.blob))
       FROM JSON_TABLE(JSON_KEYS(checkpoint->'$.channel_versions'), '$[*]' COLUMNS (channel VARCHAR(255) PATH '$')) AS keys
       INNER JOIN checkpoint_blobs bl
         ON bl.thread_id = cp.thread_id
         AND bl.checkpoint_ns = cp.checkpoint_ns
         AND bl.channel = keys.channel
         AND bl.version = JSON_EXTRACT(checkpoint->'$.channel_versions', CONCAT('$.', keys.channel))
      ) AS channel_values,
      (SELECT JSON_ARRAYAGG(JSON_ARRAY(cw.task_id, cw.channel, cw.type, cw.blob))
       FROM checkpoint_writes cw
       WHERE cw.thread_id = cp.thread_id
         AND cw.checkpoint_ns = cp.checkpoint_ns
         AND cw.checkpoint_id = cp.checkpoint_id
       ORDER BY cw.task_id, cw.idx
      ) AS pending_writes,
      (SELECT JSON_ARRAYAGG(JSON_ARRAY(cw.type, cw.blob))
       FROM checkpoint_writes cw
       WHERE cw.thread_id = cp.thread_id
         AND cw.checkpoint_ns = cp.checkpoint_ns
         AND cw.checkpoint_id = cp.parent_checkpoint_id
         AND cw.channel = '${TASKS}'
       ORDER BY cw.idx
      ) AS pending_sends
    FROM checkpoints cp`,

    // 更新或插入检查点Blob的SQL
    UPSERT_CHECKPOINT_BLOBS_SQL: `INSERT INTO checkpoint_blobs (thread_id, checkpoint_ns, channel, version, type, blob)
    VALUES (?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE type = VALUES(type), blob = VALUES(blob)`,

    // 更新或插入检查点的SQL
    UPSERT_CHECKPOINTS_SQL: `INSERT INTO checkpoints (thread_id, checkpoint_ns, checkpoint_id, parent_checkpoint_id, checkpoint, metadata)
    VALUES (?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
      checkpoint = VALUES(checkpoint),
      metadata = VALUES(metadata)`,

    // 更新或插入检查点写入的SQL
    UPSERT_CHECKPOINT_WRITES_SQL: `INSERT INTO checkpoint_writes (thread_id, checkpoint_ns, checkpoint_id, task_id, idx, channel, type, blob)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
      channel = VALUES(channel),
      type = VALUES(type),
      blob = VALUES(blob)`,

    // 插入检查点写入的SQL（如果存在则忽略）
    INSERT_CHECKPOINT_WRITES_SQL: `INSERT INTO checkpoint_writes (thread_id, checkpoint_ns, checkpoint_id, task_id, idx, channel, type, blob)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
      channel = channel`  // 不做任何更改，仅防止错误
  };
};

/**
 * 检查表是否存在的SQL
 * @param table 表名
 * @returns SQL语句
 */
export const tableExistsSQL = (table: string) => {
  return `SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = '${table}'
  ) AS table_exists`;
};
