import type { RunnableConfig } from "@langchain/core/runnables";
import {
  BaseCheckpointSaver,
  type Checkpoint,
  type CheckpointListOptions,
  type CheckpointTuple,
  type SerializerProtocol,
  type PendingWrite,
  type CheckpointMetadata,
  type ChannelVersions,
  WRITES_IDX_MAP,
} from "@langchain/langgraph-checkpoint";
import { DataSource, Repository } from "typeorm";

import { runMigrations } from "./migrations.js";
import { getSQLStatements } from "./sql.js";
import { Checkpoint as CheckpointEntity, CheckpointBlob, CheckpointWrite } from "./entities.js";

/**
 * MySQL Saver配置选项
 */
interface MySQLSaverOptions {
  /**
   * 是否自动设置数据库（创建表和运行迁移）
   * 默认为false，首次使用时需要手动调用setup()方法
   */
  autoSetup?: boolean;

  /**
   * 数据库schema名称
   * 默认为public
   */
  schema?: string;
}

/**
 * 默认配置选项
 */
const _defaultOptions: MySQLSaverOptions = {
  autoSetup: false,
  schema: 'public',
};

/**
 * 确保配置选项完整
 * @param options 配置选项
 * @returns 完整的配置选项
 */
const _ensureCompleteOptions = (
  options?: Partial<MySQLSaverOptions>
): MySQLSaverOptions => {
  return {
    ...options,
    autoSetup: options?.autoSetup ?? _defaultOptions.autoSetup,
    schema: options?.schema ?? _defaultOptions.schema,
  };
};

/**
 * LangGraph checkpointer，使用MySQL作为后端存储。
 * 内部使用TypeORM连接到MySQL数据库。
 * 
 * @example
 * ```
 * import { ChatOpenAI } from "@langchain/openai";
 * import { MySQLSaver } from "@langchain/langgraph-checkpoint-mysql";
 * import { createReactAgent } from "@langchain/langgraph/prebuilt";
 * 
 * const checkpointer = MySQLSaver.fromConfig({
 *   host: "localhost",
 *   port: 3306,
 *   username: "user",
 *   password: "password",
 *   database: "langgraph",
 *   // 可选配置选项
 *   options: {
 *     autoSetup: true // 默认为false
 *   }
 * });
 * 
 * // 注意：首次使用checkpointer时，需要调用.setup()方法
 * // 如果autoSetup设置为true，则不需要手动调用
 * if (!checkpointer.options.autoSetup) {
 *   await checkpointer.setup();
 * }
 * 
 * const graph = createReactAgent({
 *   tools: [getWeather],
 *   llm: new ChatOpenAI({
 *     model: "gpt-4o-mini",
 *   }),
 *   checkpointSaver: checkpointer,
 * });
 * const config = { configurable: { thread_id: "1" } };
 * 
 * await graph.invoke({
 *   messages: [{
 *     role: "user",
 *     content: "what's the weather in sf"
 *   }],
 * }, config);
 * ```
 */
export class MySQLSaver extends BaseCheckpointSaver {
  private readonly dataSource: DataSource;
  private readonly options: MySQLSaverOptions;
  private readonly SQL_STATEMENTS: ReturnType<typeof getSQLStatements>;
  protected isSetup: boolean;
  protected readonly serde: SerializerProtocol;
  private checkpointRepository?: Repository<CheckpointEntity>;
  private checkpointBlobRepository?: Repository<CheckpointBlob>;
  private checkpointWriteRepository?: Repository<CheckpointWrite>;

  constructor(
    dataSource: DataSource,
    serde?: SerializerProtocol,
    options?: Partial<MySQLSaverOptions>
  ) {
    super(serde);
    this.dataSource = dataSource;
    this.isSetup = false;
    this.serde = serde || super.serde;
    this.options = _ensureCompleteOptions(options);
    this.SQL_STATEMENTS = getSQLStatements();
    
    // 如果设置了自动设置，则在构造函数中初始化
    if (this.options.autoSetup) {
      this.setup().catch((err) => {
        console.error("Failed to auto-setup MySQL checkpointer:", err);
      });
    }
  }

  /**
   * 从TypeORM配置创建MySQLSaver实例
   * 
   * @param config TypeORM数据源配置
   * @param options 可选的MySQLSaver配置选项
   * @returns MySQLSaver实例
   */
  static fromConfig(config: {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    options?: Partial<MySQLSaverOptions>;
  }): MySQLSaver {
    const { host, port, username, password, database, options } = config;
    
    const dataSource = new DataSource({
      type: "mysql",
      host,
      port,
      username,
      password,
      database,
      synchronize: false,
      entities: [CheckpointEntity, CheckpointBlob, CheckpointWrite],
    });
    
    return new MySQLSaver(dataSource, undefined, options);
  }

  /**
   * 异步设置checkpoint数据库
   * 
   * 此方法在MySQL数据库中创建必要的表（如果它们不存在）并运行数据库迁移。
   * 首次使用checkpointer时，必须由用户直接调用此方法。
   */
  async setup(): Promise<void> {
    if (this.isSetup) {
      return;
    }
    
    // 初始化数据源连接
    if (!this.dataSource.isInitialized) {
      await this.dataSource.initialize();
    }
    
    // 初始化存储库
    this.checkpointRepository = this.dataSource.getRepository(CheckpointEntity);
    this.checkpointBlobRepository = this.dataSource.getRepository(CheckpointBlob);
    this.checkpointWriteRepository = this.dataSource.getRepository(CheckpointWrite);
    
    // 运行迁移
    await runMigrations(this.dataSource);
    
    this.isSetup = true;
  }
  
  /**
   * 加载检查点
   */
  private async _loadCheckpoint(
    checkpoint: Omit<Checkpoint, "pending_sends" | "channel_values">,
    channelValues: [Buffer, Buffer, Buffer?][],
    pendingSends: [Buffer, Buffer][]
  ): Promise<Checkpoint> {
    const channelValuesObj = await this._loadBlobs(channelValues);
    
    return {
      ...checkpoint,
      channel_values: channelValuesObj,
      pending_sends: pendingSends.map(([type, blob]) => [
        this.serde.deserialize(type) as string,
        this.serde.deserialize(blob),
      ]),
    };
  }
  
  /**
   * 加载Blob数据
   */
  private async _loadBlobs(
    blobValues: [Buffer, Buffer, Buffer?][]
  ): Promise<Record<string, unknown>> {
    const result: Record<string, unknown> = {};
    
    if (!blobValues) {
      return result;
    }
    
    for (const [channelBuf, typeBuf, blobBuf] of blobValues) {
      if (!channelBuf || !typeBuf) continue;
      
      const channel = this.serde.deserialize(channelBuf) as string;
      const type = this.serde.deserialize(typeBuf) as string;
      
      if (blobBuf) {
        result[channel] = this.serde.deserialize(blobBuf);
      } else {
        result[channel] = type === "undefined" ? undefined : null;
      }
    }
    
    return result;
  }
  
  /**
   * 加载元数据
   */
  private _loadMetadata(metadata: Record<string, unknown>): CheckpointMetadata {
    return metadata as CheckpointMetadata;
  }
  
  /**
   * 加载写入数据
   */
  private async _loadWrites(
    writes: [Buffer, Buffer, Buffer, Buffer][]
  ): Promise<[string, string, unknown][]> {
    if (!writes) {
      return [];
    }
    
    return writes.map(([taskIdBuf, channelBuf, typeBuf, blobBuf]) => {
      const taskId = this.serde.deserialize(taskIdBuf) as string;
      const channel = this.serde.deserialize(channelBuf) as string;
      const value = this.serde.deserialize(blobBuf);
      
      return [taskId, channel, value];
    });
  }
  
  /**
   * 序列化Blob数据
   */
  private _dumpBlobs(
    threadId: string,
    checkpointNs: string,
    values: Record<string, unknown>,
    versions: ChannelVersions
  ): [string, string, string, string, string, Buffer | undefined][] {
    const result: [string, string, string, string, string, Buffer | undefined][] = [];
    
    for (const [channel, version] of Object.entries(versions)) {
      const value = values[channel];
      const type = typeof value;
      let blob: Buffer | undefined;
      
      if (value !== undefined && value !== null) {
        blob = this.serde.serialize(value);
      }
      
      result.push([threadId, checkpointNs, channel, version as string, type, blob]);
    }
    
    return result;
  }
  
  /**
   * 序列化检查点
   */
  private _dumpCheckpoint(checkpoint: Checkpoint): Record<string, any> {
    const { channel_values, pending_sends, ...rest } = checkpoint;
    
    return rest;
  }
  
  /**
   * 序列化元数据
   */
  private _dumpMetadata(metadata: CheckpointMetadata): Record<string, any> {
    return metadata;
  }
  
  /**
   * 序列化写入数据
   */
  private _dumpWrites(
    threadId: string,
    checkpointNs: string,
    checkpointId: string,
    taskId: string,
    writes: [string, unknown][]
  ): [string, string, string, string, number, string, string, Buffer][] {
    return writes.map(([channel, value], idx) => {
      const type = typeof value;
      const blob = this.serde.serialize(value);
      
      return [
        threadId,
        checkpointNs,
        checkpointId,
        taskId,
        idx,
        channel,
        type,
        blob,
      ];
    });
  }
  
  /**
   * 构建查询的WHERE子句
   */
  private _searchWhere(
    config?: RunnableConfig,
    filter?: Record<string, unknown>,
    before?: RunnableConfig
  ): [string, unknown[]] {
    const predicates: string[] = [];
    const args: unknown[] = [];
    
    if (config?.configurable) {
      const { thread_id, checkpoint_ns, checkpoint_id } = config.configurable;
      
      if (thread_id !== undefined) {
        predicates.push("thread_id = ?");
        args.push(thread_id);
      }
      
      if (checkpoint_ns !== undefined) {
        predicates.push("checkpoint_ns = ?");
        args.push(checkpoint_ns);
      }
      
      if (checkpoint_id !== undefined) {
        predicates.push("checkpoint_id = ?");
        args.push(checkpoint_id);
      }
    }
    
    if (before?.configurable?.checkpoint_id) {
      predicates.push("checkpoint_id < ?");
      args.push(before.configurable.checkpoint_id);
    }
    
    if (filter) {
      for (const [key, value] of Object.entries(filter)) {
        if (key === "metadata" && typeof value === "object" && value !== null) {
          for (const [metaKey, metaValue] of Object.entries(value)) {
            predicates.push(`JSON_EXTRACT(metadata, '$.${metaKey}') = ?`);
            args.push(metaValue);
          }
        }
      }
    }
    
    if (predicates.length === 0) {
      return ["", []];
    }
    
    return [`WHERE ${predicates.join(" AND ")}`, args];
  }

  /**
   * 获取指定配置的检查点元组
   * @param config 可运行配置
   * @returns 检查点元组或undefined
   */
  async getTuple(config?: RunnableConfig): Promise<CheckpointTuple | undefined> {
    if (!this.isSetup) {
      await this.setup();
    }

    if (!this.checkpointRepository) {
      throw new Error("Checkpoint repository not initialized");
    }

    // 构建查询条件
    const [whereClause, args] = this._searchWhere(config);
    
    // 构建完整SQL查询
    const sql = `${this.SQL_STATEMENTS.SELECT_SQL} ${whereClause} ORDER BY created_at DESC LIMIT 1`;
    
    // 执行查询
    const result = await this.dataSource.query(sql, args);
    
    if (!result || result.length === 0) {
      return undefined;
    }
    
    const row = result[0];
    const checkpoint = {
      thread_id: row.thread_id,
      checkpoint_id: row.checkpoint_id,
      checkpoint_ns: row.checkpoint_ns,
      task_id: row.task_id,
      created_at: row.created_at,
      metadata: row.metadata ? JSON.parse(row.metadata) : {},
      versions: row.versions ? JSON.parse(row.versions) : {},
      channel_values: {},
      pending_sends: [],
    };
    
    // 获取关联的blob数据
    const blobsSql = `SELECT channel, type, blob FROM checkpoint_blobs 
      WHERE thread_id = ? AND checkpoint_ns = ? AND checkpoint_id = ?`;
    const blobsResult = await this.dataSource.query(blobsSql, [
      row.thread_id,
      row.checkpoint_ns,
      row.checkpoint_id,
    ]);
    
    // 获取关联的写入数据
    const writesSql = `SELECT task_id, channel, type, blob FROM checkpoint_writes 
      WHERE thread_id = ? AND checkpoint_ns = ? AND checkpoint_id = ?`;
    const writesResult = await this.dataSource.query(writesSql, [
      row.thread_id,
      row.checkpoint_ns,
      row.checkpoint_id,
    ]);
    
    // 处理blob数据
    const channelValues: [Buffer, Buffer, Buffer?][] = blobsResult.map(
      (blob: { channel: string; type: string; blob: Buffer }) => {
        const channelBuf = this.serde.serialize(blob.channel);
        const typeBuf = this.serde.serialize(blob.type);
        return [channelBuf, typeBuf, blob.blob];
      }
    );
    
    // 处理写入数据
    const pendingSends: [Buffer, Buffer][] = writesResult.map(
      (write: { task_id: string; channel: string; type: string; blob: Buffer }) => {
        const typeBuf = this.serde.serialize(write.type);
        return [typeBuf, write.blob];
      }
    );
    
    // 加载完整的检查点
    const fullCheckpoint = await this._loadCheckpoint(
      checkpoint,
      channelValues,
      pendingSends
    );
    
    return {
      config: {
        configurable: {
          thread_id: fullCheckpoint.thread_id,
          checkpoint_id: fullCheckpoint.checkpoint_id,
          checkpoint_ns: fullCheckpoint.checkpoint_ns,
        },
      },
      checkpoint: fullCheckpoint,
    };
  }

  /**
   * 保存检查点
   * @param checkpoint 检查点数据
   * @param config 可运行配置
   */
  async put(checkpoint: Checkpoint, config?: RunnableConfig): Promise<void> {
    if (!this.isSetup) {
      await this.setup();
    }

    if (!this.checkpointRepository) {
      throw new Error("Checkpoint repository not initialized");
    }
    
    const threadId = checkpoint.thread_id;
    const checkpointId = checkpoint.checkpoint_id;
    const checkpointNs = checkpoint.checkpoint_ns;
    
    // 准备检查点数据
    const checkpointData = this._dumpCheckpoint(checkpoint);
    const metadata = this._dumpMetadata(checkpoint.metadata || {});
    const versions = checkpoint.versions || {};
    
    // 准备blob数据
    const blobs = this._dumpBlobs(
      threadId,
      checkpointNs,
      checkpoint.channel_values,
      versions
    );
    
    // 开始事务
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // 插入检查点
      await queryRunner.query(this.SQL_STATEMENTS.UPSERT_CHECKPOINTS_SQL, [
        threadId,
        checkpointNs,
        checkpointId,
        checkpoint.task_id,
        JSON.stringify(metadata),
        JSON.stringify(versions),
        new Date(),
        // ON DUPLICATE KEY UPDATE 部分的参数
        JSON.stringify(metadata),
        JSON.stringify(versions),
        new Date(),
      ]);
      
      // 插入blob数据
      for (const [threadId, checkpointNs, channel, version, type, blob] of blobs) {
        if (blob) {
          await queryRunner.query(this.SQL_STATEMENTS.UPSERT_CHECKPOINT_BLOBS_SQL, [
            threadId,
            checkpointNs,
            checkpointId,
            channel,
            version,
            type,
            blob,
            // ON DUPLICATE KEY UPDATE 部分的参数
            version,
            type,
            blob,
          ]);
        }
      }
      
      // 提交事务
      await queryRunner.commitTransaction();
    } catch (error) {
      // 回滚事务
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // 释放查询运行器
      await queryRunner.release();
    }
  }

  /**
   * 保存写入数据
   * @param writes 写入数据
   * @param config 可运行配置
   */
  async putWrites(writes: PendingWrite[], config?: RunnableConfig): Promise<void> {
    if (!this.isSetup) {
      await this.setup();
    }

    if (!this.checkpointRepository) {
      throw new Error("Checkpoint repository not initialized");
    }
    
    if (!config?.configurable) {
      throw new Error("Config must have configurable property");
    }
    
    const { thread_id, checkpoint_id, checkpoint_ns } = config.configurable;
    
    if (!thread_id || !checkpoint_id || !checkpoint_ns) {
      throw new Error("Config must have thread_id, checkpoint_id, and checkpoint_ns");
    }
    
    // 准备写入数据
    const allWrites: [string, string, string, string, number, string, string, Buffer][] = [];
    
    for (const write of writes) {
      const { task_id, writes: taskWrites } = write;
      
      const formattedWrites = this._dumpWrites(
        thread_id as string,
        checkpoint_ns as string,
        checkpoint_id as string,
        task_id,
        taskWrites
      );
      
      allWrites.push(...formattedWrites);
    }
    
    // 开始事务
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // 插入写入数据
      for (const [
        threadId,
        checkpointNs,
        checkpointId,
        taskId,
        idx,
        channel,
        type,
        blob,
      ] of allWrites) {
        await queryRunner.query(this.SQL_STATEMENTS.UPSERT_CHECKPOINT_WRITES_SQL, [
          threadId,
          checkpointNs,
          checkpointId,
          taskId,
          idx,
          channel,
          type,
          blob,
          // ON DUPLICATE KEY UPDATE 部分的参数
          type,
          blob,
        ]);
      }
      
      // 提交事务
      await queryRunner.commitTransaction();
    } catch (error) {
      // 回滚事务
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // 释放查询运行器
      await queryRunner.release();
    }
  }

  /**
   * 列出检查点
   * @param options 列表选项
   * @returns 检查点元组数组
   */
  async list(options?: CheckpointListOptions): Promise<CheckpointTuple[]> {
    if (!this.isSetup) {
      await this.setup();
    }

    if (!this.checkpointRepository) {
      throw new Error("Checkpoint repository not initialized");
    }
    
    const { config, filter, before, limit } = options || {};
    
    // 构建查询条件
    const [whereClause, args] = this._searchWhere(config, filter, before);
    
    // 构建完整SQL查询
    let sql = `${this.SQL_STATEMENTS.SELECT_SQL} ${whereClause} ORDER BY created_at DESC`;
    
    if (limit) {
      sql += ` LIMIT ${limit}`;
    }
    
    // 执行查询
    const result = await this.dataSource.query(sql, args);
    
    if (!result || result.length === 0) {
      return [];
    }
    
    // 处理结果
    const checkpoints: CheckpointTuple[] = [];
    
    for (const row of result) {
      const checkpoint = {
        thread_id: row.thread_id,
        checkpoint_id: row.checkpoint_id,
        checkpoint_ns: row.checkpoint_ns,
        task_id: row.task_id,
        created_at: row.created_at,
        metadata: row.metadata ? JSON.parse(row.metadata) : {},
        versions: row.versions ? JSON.parse(row.versions) : {},
        channel_values: {},
        pending_sends: [],
      };
      
      // 获取关联的blob数据
      const blobsSql = `SELECT channel, type, blob FROM checkpoint_blobs 
        WHERE thread_id = ? AND checkpoint_ns = ? AND checkpoint_id = ?`;
      const blobsResult = await this.dataSource.query(blobsSql, [
        row.thread_id,
        row.checkpoint_ns,
        row.checkpoint_id,
      ]);
      
      // 获取关联的写入数据
      const writesSql = `SELECT task_id, channel, type, blob FROM checkpoint_writes 
        WHERE thread_id = ? AND checkpoint_ns = ? AND checkpoint_id = ?`;
      const writesResult = await this.dataSource.query(writesSql, [
        row.thread_id,
        row.checkpoint_ns,
        row.checkpoint_id,
      ]);
      
      // 处理blob数据
      const channelValues: [Buffer, Buffer, Buffer?][] = blobsResult.map(
        (blob: { channel: string; type: string; blob: Buffer }) => {
          const channelBuf = this.serde.serialize(blob.channel);
          const typeBuf = this.serde.serialize(blob.type);
          return [channelBuf, typeBuf, blob.blob];
        }
      );
      
      // 处理写入数据
      const pendingSends: [Buffer, Buffer][] = writesResult.map(
        (write: { task_id: string; channel: string; type: string; blob: Buffer }) => {
          const typeBuf = this.serde.serialize(write.type);
          return [typeBuf, write.blob];
        }
      );
      
      // 加载完整的检查点
      const fullCheckpoint = await this._loadCheckpoint(
        checkpoint,
        channelValues,
        pendingSends
      );
      
      checkpoints.push({
        config: {
          configurable: {
            thread_id: fullCheckpoint.thread_id,
            checkpoint_id: fullCheckpoint.checkpoint_id,
            checkpoint_ns: fullCheckpoint.checkpoint_ns,
          },
        },
        checkpoint: fullCheckpoint,
      });
    }
    
    return checkpoints;
  }
}
