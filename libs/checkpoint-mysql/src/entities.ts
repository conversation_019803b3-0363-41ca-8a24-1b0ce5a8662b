import { Entity, Column, PrimaryColumn } from 'typeorm';

/**
 * 检查点迁移表实体
 */
@Entity('checkpoint_migrations')
export class CheckpointMigration {
  @PrimaryColumn('int')
  v!: number;
}

/**
 * 检查点表实体
 */
@Entity('checkpoints')
export class Checkpoint {
  @PrimaryColumn('varchar')
  thread_id!: string;

  @PrimaryColumn('varchar', { default: '' })
  checkpoint_ns!: string;

  @PrimaryColumn('varchar')
  checkpoint_id!: string;

  @Column('varchar', { nullable: true })
  parent_checkpoint_id?: string;

  @Column('varchar', { nullable: true })
  type?: string;

  @Column('json')
  checkpoint!: Record<string, any>;

  @Column('json', { default: '{}' })
  metadata!: Record<string, any>;
}

/**
 * 检查点Blob表实体
 */
@Entity('checkpoint_blobs')
export class CheckpointBlob {
  @PrimaryColumn('varchar')
  thread_id!: string;

  @PrimaryColumn('varchar', { default: '' })
  checkpoint_ns!: string;

  @PrimaryColumn('varchar')
  channel!: string;

  @PrimaryColumn('varchar')
  version!: string;

  @Column('varchar')
  type!: string;

  @Column('blob', { nullable: true })
  blob?: Buffer;
}

/**
 * 检查点写入表实体
 */
@Entity('checkpoint_writes')
export class CheckpointWrite {
  @PrimaryColumn('varchar')
  thread_id!: string;

  @PrimaryColumn('varchar', { default: '' })
  checkpoint_ns!: string;

  @PrimaryColumn('varchar')
  checkpoint_id!: string;

  @PrimaryColumn('varchar')
  task_id!: string;

  @PrimaryColumn('int')
  idx!: number;

  @Column('varchar')
  channel!: string;

  @Column('varchar', { nullable: true })
  type?: string;

  @Column('blob')
  blob!: Buffer;
}
