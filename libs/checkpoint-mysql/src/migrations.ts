import { DataSource } from 'typeorm';
import { CheckpointMigration, Checkpoint, CheckpointBlob, CheckpointWrite } from './entities.js';

/**
 * 获取数据库迁移SQL语句
 * 要添加新的迁移，请在getMigrations函数返回的列表中添加新的字符串。
 * 迁移在列表中的位置就是版本号。
 */
export const getMigrations = () => {
  return [
    // 版本 0: 创建迁移表
    `CREATE TABLE IF NOT EXISTS checkpoint_migrations (
      v INT PRIMARY KEY
    )`,
    
    // 版本 1: 创建检查点表
    `CREATE TABLE IF NOT EXISTS checkpoints (
      thread_id VARCHAR(255) NOT NULL,
      checkpoint_ns VARCHAR(255) NOT NULL DEFAULT '',
      checkpoint_id VARCHAR(255) NOT NULL,
      parent_checkpoint_id VARCHAR(255),
      type VARCHAR(255),
      checkpoint JSON NOT NULL,
      metadata JSON NOT NULL DEFAULT '{}',
      PRIMARY KEY (thread_id, checkpoint_ns, checkpoint_id)
    )`,
    
    // 版本 2: 创建检查点Blob表
    `CREATE TABLE IF NOT EXISTS checkpoint_blobs (
      thread_id VARCHAR(255) NOT NULL,
      checkpoint_ns VARCHAR(255) NOT NULL DEFAULT '',
      channel VARCHAR(255) NOT NULL,
      version VARCHAR(255) NOT NULL,
      type VARCHAR(255) NOT NULL,
      blob LONGBLOB,
      PRIMARY KEY (thread_id, checkpoint_ns, channel, version)
    )`,
    
    // 版本 3: 创建检查点写入表
    `CREATE TABLE IF NOT EXISTS checkpoint_writes (
      thread_id VARCHAR(255) NOT NULL,
      checkpoint_ns VARCHAR(255) NOT NULL DEFAULT '',
      checkpoint_id VARCHAR(255) NOT NULL,
      task_id VARCHAR(255) NOT NULL,
      idx INT NOT NULL,
      channel VARCHAR(255) NOT NULL,
      type VARCHAR(255),
      blob LONGBLOB NOT NULL,
      PRIMARY KEY (thread_id, checkpoint_ns, checkpoint_id, task_id, idx)
    )`,
    
    // 版本 4: 修改blob字段为可空
    `ALTER TABLE checkpoint_blobs MODIFY blob LONGBLOB NULL`
  ];
};

/**
 * 运行数据库迁移
 * @param dataSource TypeORM数据源
 */
export async function runMigrations(dataSource: DataSource): Promise<void> {
  const queryRunner = dataSource.createQueryRunner();
  const migrations = getMigrations();
  
  try {
    // 检查迁移表是否存在，如果不存在则创建
    await queryRunner.query(migrations[0]);
    
    // 获取当前版本
    const result = await queryRunner.query(
      'SELECT v FROM checkpoint_migrations ORDER BY v DESC LIMIT 1'
    );
    
    let currentVersion = -1;
    if (result && result.length > 0) {
      currentVersion = result[0].v;
    }
    
    // 运行迁移
    for (let v = currentVersion + 1; v < migrations.length; v++) {
      console.log(`Running migration ${v}`);
      await queryRunner.query(migrations[v]);
      await queryRunner.query('INSERT INTO checkpoint_migrations (v) VALUES (?)', [v]);
    }
  } finally {
    await queryRunner.release();
  }
}
