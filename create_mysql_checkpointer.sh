#!/bin/bash

# 创建目录结构
mkdir -p libs/checkpoint-mysql/src
mkdir -p libs/checkpoint-mysql/dist
mkdir -p libs/checkpoint-mysql/tests

# 创建基本文件
touch libs/checkpoint-mysql/package.json
touch libs/checkpoint-mysql/tsconfig.json
touch libs/checkpoint-mysql/README.md
touch libs/checkpoint-mysql/.gitignore

# 创建源代码文件
touch libs/checkpoint-mysql/src/index.ts
touch libs/checkpoint-mysql/src/entities.ts
touch libs/checkpoint-mysql/src/migrations.ts
touch libs/checkpoint-mysql/src/sql.ts

# 创建测试文件
touch libs/checkpoint-mysql/tests/mysql.test.ts

echo "目录结构已创建完成！"
